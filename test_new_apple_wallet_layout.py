#!/usr/bin/env python
"""
Script para testar a nova estrutura do Apple Wallet com layout otimizado.

Layout esperado:
- Header Fields: Pontos totalmente à direita (nome + saldo)
- Primary Fields: Vazio (apenas imagem strip)
- Secondary Fields: Nome do cliente à esquerda
- Back Fields: <PERSON><PERSON>, contato, última mensagem, link do programa
"""

import os
import sys
import django
import json
from pathlib import Path

# Setup Django
sys.path.append('/opt/zuppy')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.production')
django.setup()

from zuppy.wallet.infrastructure.apple.apple_pass_generator import ApplePassGenerator
from zuppy.loyalty.models import LoyaltyProgram, LoyaltyUser
from zuppy.company.models import Company
from django.contrib.auth import get_user_model

User = get_user_model()

def test_new_apple_wallet_layout():
    """Testa a nova estrutura do Apple Wallet."""
    
    print("🍎 Testando Nova Estrutura do Apple Wallet")
    print("=" * 50)
    
    # Dados de teste completos
    card_data = {
        "serial_number": "test_new_layout_001",
        "description": "Cartão de Fidelidade - Teste Layout",
        "barcode_value": "ZP-12345678",
        "pass_type": "storeCard",
        
        # PONTOS (Header Fields - à direita)
        "points_name": "EOS Points",
        "points_balance": 1250,
        
        # CLIENTE (Secondary Fields - à esquerda)
        "customer_name": "João Silva Santos",
        
        # PROGRAMA E EMPRESA
        "loyalty_program_id": None,  # Será preenchido se encontrar programa
        "company_name": "Restaurante EOS",
        "company_phone": "(11) 3456-7890",
        "company_email": "<EMAIL>",
        "company_website": "https://eosrestaurante.com.br",
        "program_slug": "eosrestaurante",
        
        # VERSO DO CARTÃO
        "program_rules": """• Acumule 1 EOS Point a cada R$ 1,00 gasto
• Pontos válidos por 12 meses
• Resgate a partir de 100 pontos
• Não acumulativo com outras promoções""",
        
        "last_message": "Parabéns! Você ganhou 50 EOS Points na sua última visita!",
        
        # CORES
        "background_color": "#1E40AF",  # Azul
        "foreground_color": "#FFFFFF",  # Branco
        "label_color": "#FCD34D",       # Amarelo dourado
        
        # OUTROS
        "organization_name": "Restaurante EOS",
        "logo_text": "EOS",
    }
    
    try:
        # Tentar encontrar um programa real para usar dados reais
        try:
            program = LoyaltyProgram.objects.filter(is_active=True).first()
            if program:
                print(f"📋 Usando programa real: {program.name}")
                card_data.update({
                    "loyalty_program_id": program.id,
                    "points_name": program.points_name or "Pontos",
                    "program_rules": program.rules or card_data["program_rules"],
                    "program_slug": program.slug or "programa",
                })
                
                if program.company:
                    card_data.update({
                        "company_name": program.company.name,
                        "company_phone": getattr(program.company, 'phone', ''),
                        "company_email": getattr(program.company, 'email', ''),
                        "company_website": getattr(program.company, 'website', ''),
                    })
            else:
                print("⚠️  Nenhum programa encontrado, usando dados de teste")
        except Exception as e:
            print(f"⚠️  Erro ao buscar programa: {e}")
        
        # Inicializar gerador
        generator = ApplePassGenerator()
        
        if not generator.is_available():
            print("❌ Apple Wallet generator não disponível")
            return
        
        print(f"✅ Gerador disponível")
        print(f"📊 Dados do cartão:")
        print(f"   • Pontos: {card_data['points_name']} = {card_data['points_balance']}")
        print(f"   • Cliente: {card_data['customer_name']}")
        print(f"   • Empresa: {card_data['company_name']}")
        print(f"   • Link: https://zupy.com/@{card_data['program_slug']}")
        
        # Testar criação da estrutura de campos
        print("\n🏗️  Testando estrutura de campos...")
        fields_structure = generator._create_storecard_fields(card_data)
        
        print("\n📱 ESTRUTURA RESULTANTE:")
        print("=" * 30)
        
        # Header Fields
        print("🔝 HEADER FIELDS (Pontos à direita):")
        for field in fields_structure.get("headerFields", []):
            alignment = field.get("textAlignment", "default")
            print(f"   • {field['key']}: '{field['label']}' = '{field['value']}' [{alignment}]")
        
        # Primary Fields
        print("\n🎯 PRIMARY FIELDS (Apenas imagem strip):")
        primary_fields = fields_structure.get("primaryFields", [])
        if primary_fields:
            for field in primary_fields:
                print(f"   • {field['key']}: '{field['label']}' = '{field['value']}'")
        else:
            print("   • VAZIO (apenas imagem strip será exibida)")
        
        # Secondary Fields
        print("\n📋 SECONDARY FIELDS (Cliente à esquerda):")
        for field in fields_structure.get("secondaryFields", []):
            alignment = field.get("textAlignment", "default")
            print(f"   • {field['key']}: '{field['label']}' = '{field['value']}' [{alignment}]")
        
        # Back Fields
        print("\n🔙 BACK FIELDS (Verso do cartão):")
        for field in fields_structure.get("backFields", []):
            value_preview = field['value'][:50] + "..." if len(field['value']) > 50 else field['value']
            print(f"   • {field['key']}: '{field['label']}'")
            print(f"     └─ {value_preview}")
        
        # Testar geração completa do pass
        print(f"\n🎫 Gerando pass completo...")
        result = generator.generate_pass(card_data, save_file=False)
        
        if result:
            pass_data, filename = result
            print(f"✅ Pass gerado com sucesso!")
            print(f"   • Arquivo: {filename}")
            print(f"   • Tamanho: {len(pass_data)} bytes")
            
            # Salvar arquivo de teste
            test_file = Path(f"/opt/zuppy/test_new_layout_{card_data['serial_number']}.pkpass")
            with open(test_file, "wb") as f:
                f.write(pass_data)
            print(f"   • Salvo em: {test_file}")
            
        else:
            print("❌ Falha ao gerar pass")
        
        print(f"\n📋 RESUMO DO LAYOUT:")
        print(f"✅ Pontos à direita: {card_data['points_name']} = {card_data['points_balance']}")
        print(f"✅ Primary field vazio (só imagem strip)")
        print(f"✅ Cliente à esquerda: {card_data['customer_name']}")
        print(f"✅ Verso com 4 seções: Regras, Contato, Última Mensagem, Link")
        print(f"✅ Link do programa: https://zupy.com/@{card_data['program_slug']}")
        
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_new_apple_wallet_layout()