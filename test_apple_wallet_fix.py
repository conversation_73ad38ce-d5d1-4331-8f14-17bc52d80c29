"""
Script de teste para validar a correção do Apple Wallet.

Este script testa a geração de cartões Apple Wallet com a implementação corrigida,
validando tanto StoreCard quanto Coupon.
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
if __name__ == "__main__":
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.production")
    django.setup()

import logging
import tempfile
from datetime import datetime, timedelta

from zuppy.wallet.infrastructure.apple.apple_pass_generator import ApplePassGenerator

logger = logging.getLogger(__name__)


def test_apple_wallet_storecard():
    """Testa geração de StoreCard (cartão de fidelidade)."""
    
    print("🧪 Testando geração de StoreCard (Fidelidade)...")
    
    generator = ApplePassGenerator()
    
    if not generator.is_available():
        print("❌ ApplePassGenerator não disponível")
        return False
        
    # Dados do cartão de fidelidade
    card_data = {
        "pass_type": "storeCard",
        "serial_number": "TEST-STORE-001",
        "description": "Cartão de Fidelidade Zupy - Teste",
        "barcode_value": "zupy://loyalty/TEST-STORE-001",
        "barcode_alt_text": "Escaneie para usar",
        
        # Campos específicos do StoreCard
        "points_balance": 150,
        "points_name": "PONTOS",
        "customer_name": "Cliente Teste",
        "customer_label": "Cliente",
        "tier": "Ouro",
        "tier_label": "Nível",
        
        # Cores
        "background_color": "#6B46C1",
        "foreground_color": "#FFFFFF", 
        "label_color": "#E5E7EB",
        
        # Logo text
        "logo_text": "ZUPY FIDELIDADE",
        
        # Dados da empresa
        "company_name": "Zupy Teste",
        "company_phone": "(11) 9999-9999",
        "company_email": "<EMAIL>",
        "company_website": "https://zupy.com",
        
        # Termos
        "terms": "Este é um cartão de teste. Válido apenas para desenvolvimento.",
        
        # WebService
        "site_url": "https://zupy.com",
        "authentication_token": "abcd1234efgh5678",  # 16 chars
    }
    
    try:
        result = generator.generate_pass(card_data)
        
        if result:
            pass_data, filename = result
            
            # Salvar arquivo para teste
            test_file = Path("/tmp") / filename
            with open(test_file, "wb") as f:
                f.write(pass_data)
                
            print(f"✅ StoreCard gerado com sucesso: {filename}")
            print(f"📄 Arquivo salvo em: {test_file}")
            print(f"📊 Tamanho: {len(pass_data)} bytes")
            
            return True
        else:
            print("❌ Falha na geração do StoreCard")
            return False
            
    except Exception as e:
        print(f"❌ Erro na geração do StoreCard: {e}")
        logger.exception("Erro detalhado:")
        return False


def test_apple_wallet_coupon():
    """Testa geração de Coupon (cupom de desconto)."""
    
    print("\n🧪 Testando geração de Coupon (Cupom)...")
    
    generator = ApplePassGenerator()
    
    if not generator.is_available():
        print("❌ ApplePassGenerator não disponível")
        return False
        
    # Data de expiração (7 dias a partir de hoje)
    expiration_date = datetime.now() + timedelta(days=7)
    
    # Dados do cupom
    card_data = {
        "pass_type": "coupon",
        "serial_number": "TEST-COUPON-001", 
        "description": "Cupom de Desconto Zupy - Teste",
        "barcode_value": "zupy://coupon/TEST-COUPON-001",
        "barcode_alt_text": "Escaneie para resgatar",
        
        # Campos específicos do Coupon
        "title": "20% de Desconto",
        "title_label": "OFERTA",
        "discount_value": "20%",
        "expiration_date": expiration_date.strftime("%Y-%m-%d"),
        
        # Cores
        "background_color": "#DC2626",
        "foreground_color": "#FFFFFF",
        "label_color": "#FEE2E2",
        
        # Logo text
        "logo_text": "ZUPY OFERTAS",
        
        # Dados da empresa
        "company_name": "Zupy Teste",
        "company_phone": "(11) 9999-9999", 
        "company_email": "<EMAIL>",
        "company_website": "https://zupy.com/ofertas",
        
        # Termos
        "terms": "Válido apenas para teste. Não pode ser combinado com outras ofertas.",
        
        # WebService
        "site_url": "https://zupy.com",
        "authentication_token": "1234abcd5678efgh",  # 16 chars
    }
    
    try:
        result = generator.generate_pass(card_data)
        
        if result:
            pass_data, filename = result
            
            # Salvar arquivo para teste
            test_file = Path("/tmp") / filename
            with open(test_file, "wb") as f:
                f.write(pass_data)
                
            print(f"✅ Coupon gerado com sucesso: {filename}")
            print(f"📄 Arquivo salvo em: {test_file}")
            print(f"📊 Tamanho: {len(pass_data)} bytes")
            
            return True
        else:
            print("❌ Falha na geração do Coupon")
            return False
            
    except Exception as e:
        print(f"❌ Erro na geração do Coupon: {e}")
        logger.exception("Erro detalhado:")
        return False


def validate_configuration():
    """Valida se as configurações estão corretas."""
    
    print("🔧 Validando configurações...")
    
    generator = ApplePassGenerator()
    
    if generator.is_available():
        config = generator.config
        print(f"📋 Pass Type Identifier: {config.get('pass_type_identifier')}")
        print(f"🏢 Team Identifier: {config.get('team_identifier')}")
        print(f"🏪 Organization Name: {config.get('organization_name')}")
        print(f"📜 Certificate Path: {config.get('certificate_path')}")
        print(f"🔒 WWDR Certificate Path: {config.get('wwdr_certificate_path')}")
        
        # Verificar se arquivos existem
        cert_exists = os.path.exists(config.get('certificate_path', ''))
        wwdr_exists = os.path.exists(config.get('wwdr_certificate_path', ''))
        
        print(f"📜 Certificate exists: {'✅' if cert_exists else '❌'}")
        print(f"🔒 WWDR Certificate exists: {'✅' if wwdr_exists else '❌'}")
        print(f"📚 Library available: {'✅' if generator.is_available() else '❌'}")
        
        return generator.is_available() and cert_exists and wwdr_exists
    else:
        print("❌ Generator not available")
        return False


def main():
    """Função principal do teste."""
    
    print("=" * 60)
    print("🍎 TESTE DE CORREÇÃO APPLE WALLET")
    print("=" * 60)
    
    # Validar configuração
    config_ok = validate_configuration()
    
    if not config_ok:
        print("\n❌ Configuração inválida. Verifique os certificados e settings.")
        return False
        
    print("\n✅ Configuração válida. Iniciando testes...")
    
    # Teste StoreCard
    storecard_ok = test_apple_wallet_storecard()
    
    # Teste Coupon
    coupon_ok = test_apple_wallet_coupon()
    
    # Resultado final
    print("\n" + "=" * 60)
    print("📊 RESULTADO DOS TESTES")
    print("=" * 60)
    print(f"StoreCard: {'✅ PASSOU' if storecard_ok else '❌ FALHOU'}")
    print(f"Coupon: {'✅ PASSOU' if coupon_ok else '❌ FALHOU'}")
    
    success = storecard_ok and coupon_ok
    
    if success:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("💡 Os cartões estão sendo gerados corretamente.")
        print("📱 Teste os arquivos .pkpass em um iPhone para validar o reconhecimento.")
    else:
        print("\n⚠️  ALGUNS TESTES FALHARAM!")
        print("🔍 Verifique os logs para mais detalhes.")
        
    print("\n📂 Arquivos de teste salvos em /tmp/")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)