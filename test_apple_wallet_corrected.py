#!/usr/bin/env python3
"""
Script para testar a geração corrigida do Apple Wallet para o programa EOS Restaurant.
Verifica se os campos estão posicionados corretamente e se as imagens são processadas.
"""

import os
import sys
import django
import json
from pathlib import Path

# Configurar Django
sys.path.append('/opt/zuppy')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

from zuppy.loyalty.models import LoyaltyProgram, LoyaltyUser
from zuppy.wallet.infrastructure.apple.apple_pass_generator import ApplePassGenerator
from django.contrib.auth import get_user_model

User = get_user_model()

def test_corrected_apple_wallet():
    """Testa a geração corrigida do Apple Wallet."""
    
    print("🔍 Testando Apple Wallet com correções aplicadas...")
    
    # 1. Buscar programa EOS Restaurant
    try:
        program = LoyaltyProgram.objects.get(slug='eosrestaurante')
        print(f"✅ Programa encontrado: {program.name}")
        print(f"   - Pontos: {program.points_name}")
        print(f"   - Cores: BG={program.background_color}, FG={program.foreground_color}")
        
        # Verificar imagens do programa
        images_info = []
        if program.icon_image:
            images_info.append(f"Icon: {program.icon_image.url}")
        if program.logo:
            images_info.append(f"Logo: {program.logo.url}")
        if program.strip_image:
            images_info.append(f"Strip: {program.strip_image.url}")
        
        if images_info:
            print(f"   - Imagens: {', '.join(images_info)}")
        else:
            print("   - ⚠️  Nenhuma imagem configurada")
            
    except LoyaltyProgram.DoesNotExist:
        print("❌ Programa EOS Restaurant não encontrado")
        return
    
    # 2. Buscar ou criar usuário de teste
    try:
        user = User.objects.get(email='<EMAIL>')
        print(f"✅ Usuário encontrado: {user.email}")
    except User.DoesNotExist:
        print("❌ Usuário webmaster não encontrado")
        return
    
    # 3. Buscar ou criar LoyaltyUser
    try:
        loyalty_user = LoyaltyUser.objects.get(user=user, program=program)
        print(f"✅ LoyaltyUser encontrado - Pontos: {loyalty_user.points_balance}")
    except LoyaltyUser.DoesNotExist:
        # Criar LoyaltyUser de teste
        loyalty_user = LoyaltyUser.objects.create(
            user=user,
            program=program,
            points_balance=150,  # 150 pérolas
            tier='bronze'
        )
        print(f"✅ LoyaltyUser criado - Pontos: {loyalty_user.points_balance}")
    
    # 4. Testar nosso gerador corrigido
    print("\n🔧 Testando geração com Apple Pass Generator corrigido...")
    
    try:
        generator = ApplePassGenerator()
        
        # Criar card_data como faria o DownloadPkpassService
        card_data = {
            "customer_name": loyalty_user.user.get_full_name() or loyalty_user.user.username,
            "points_balance": loyalty_user.points_balance or 0,
            "loyalty_program": program,
            "loyalty_user": loyalty_user,
            "tier": loyalty_user.tier,
            "qr_value": f"https://zupy.com/s/{loyalty_user.id}",
            "serial_number": str(loyalty_user.id),
            "pass_type_identifier": "pass.com.zupy.clube.public",
            "organization_name": program.company.name,
            "description": f"Cartão de Fidelidade {program.name}",
            "logo_text": program.logo_text or program.name,
            "foreground_color": program.get_font_color(),
            "background_color": program.get_bg_color(),
            "label_color": program.get_header_color()
        }
        
        print(f"   - Nome do cliente: {card_data['customer_name']}")
        print(f"   - Pontos: {card_data['points_balance']} {program.points_name}")
        print(f"   - QR Code: {card_data['qr_value']}")
        
        # 5. Testar criação da estrutura de campos
        print("\n📋 Testando estrutura de campos corrigida...")
        
        fields_structure = generator._create_storecard_fields(card_data)
        
        print("✅ Estrutura de campos gerada:")
        print(f"   - Header Fields: {len(fields_structure.get('headerFields', []))} campos")
        for field in fields_structure.get('headerFields', []):
            print(f"     • {field.get('label', 'N/A')}: {field.get('value', 'N/A')} (align: {field.get('textAlignment', 'default')})")
        
        print(f"   - Primary Fields: {len(fields_structure.get('primaryFields', []))} campos")
        for field in fields_structure.get('primaryFields', []):
            print(f"     • {field.get('label', 'N/A')}: {field.get('value', 'N/A')}")
        
        print(f"   - Secondary Fields: {len(fields_structure.get('secondaryFields', []))} campos")
        for field in fields_structure.get('secondaryFields', []):
            print(f"     • {field.get('label', 'N/A')}: {field.get('value', 'N/A')} (align: {field.get('textAlignment', 'default')})")
        
        print(f"   - Back Fields: {len(fields_structure.get('backFields', []))} campos")
        for field in fields_structure.get('backFields', []):
            print(f"     • {field.get('label', 'N/A')}: {field.get('value', 'N/A')[:50]}...")
        
        # 6. Verificar se usa dados do LoyaltyProgram
        print("\n🎯 Verificando origem dos dados...")
        
        header_field = fields_structure.get('headerFields', [{}])[0] if fields_structure.get('headerFields') else {}
        if header_field.get('label') == program.points_name:
            print(f"✅ Pontos corretos: {header_field.get('label')} (origem: LoyaltyProgram)")
        else:
            print(f"❌ Pontos incorretos: {header_field.get('label')} (esperado: {program.points_name})")
        
        # 7. Testar geração completa (opcional)
        print("\n💾 Testando geração completa do arquivo .pkpass...")
        
        try:
            pass_bytes = generator._generate_pass_from_card_data(card_data)
            
            if pass_bytes and len(pass_bytes) > 0:
                print(f"✅ Arquivo .pkpass gerado com sucesso ({len(pass_bytes)} bytes)")
                
                # Salvar para teste
                output_file = f"/tmp/eos_test_card_{loyalty_user.id}.pkpass"
                with open(output_file, 'wb') as f:
                    f.write(pass_bytes)
                print(f"   - Arquivo salvo em: {output_file}")
                
            else:
                print("❌ Erro: arquivo .pkpass vazio")
                
        except Exception as e:
            print(f"❌ Erro na geração completa: {e}")
        
        print("\n🎉 Teste concluído!")
        print("\nResumo das correções aplicadas:")
        print("  ✅ Header Fields: pontos totalmente à direita")
        print("  ✅ Primary Fields: vazio (apenas strip image)")
        print("  ✅ Secondary Fields: nome do cliente à esquerda")  
        print("  ✅ Back Fields: regras, contato, mensagem, link do programa")
        print("  ✅ Dados vêm do LoyaltyProgram (não de templates)")
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_corrected_apple_wallet()