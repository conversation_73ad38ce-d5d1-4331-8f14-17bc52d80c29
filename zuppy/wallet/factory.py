"""
Factory for wallet pass generators and services.

This module provides factory functions for creating wallet pass generators
and services.
"""

import logging
import os
from typing import Dict

from zuppy.wallet.application.interfaces.pass_generator import PassGenerator
from zuppy.wallet.application.services.generate_pass import PassGenerationService
from zuppy.wallet.infrastructure.apple.apple_pass_generator import ApplePassGenerator
from zuppy.wallet.infrastructure.google.google_pass_generator import GoogleWalletGenerator
from zuppy.wallet.infrastructure.web.web_pass_generator import WebPassGenerator

logger = logging.getLogger(__name__)


def create_pass_generators() -> Dict[str, PassGenerator]:
    """
    Create instances of all available pass generators.
    
    Returns:
        Dictionary mapping platform names to generator instances
    """
    generators = {}
    
    # Create Apple Wallet generator
    try:
        apple_generator = ApplePassGenerator()
        # Check if certificates are available
        cert_path = os.environ.get('APPLE_CERTIFICATE_PATH', '')
        wwdr_path = os.environ.get('APPLE_WWDR_CERTIFICATE_PATH', '')
        if os.path.exists(cert_path) and os.path.exists(wwdr_path):
            generators['apple'] = apple_generator
            logger.info(f"Apple Wallet generator loaded successfully")
        else:
            logger.warning(f"Apple Wallet certificates not found")
    except Exception as e:
        logger.error(f"Error initializing Apple Wallet generator: {e}")
        
    # Create Google Wallet generator
    try:
        google_generator = GoogleWalletGenerator()
        # Check if Google Service Account JSON is available
        service_account_path = os.environ.get('GOOGLE_SERVICE_ACCOUNT_JSON_PATH', '')
        if hasattr(google_generator, 'is_available'):
            if google_generator.is_available():
                generators['google'] = google_generator
                logger.info(f"Google Wallet generator loaded successfully")
            else:
                logger.warning(f"Google Wallet credentials not available")
        elif os.path.exists(service_account_path):
            generators['google'] = google_generator
            logger.info(f"Google Wallet generator loaded successfully")
        else:
            logger.warning(f"Google Wallet service account JSON not found")
    except Exception as e:
        logger.error(f"Error initializing Google Wallet generator: {e}")
        
    # Create Web generator (always available)
    try:
        web_generator = WebPassGenerator()
        generators['web'] = web_generator
        logger.info(f"Web pass generator loaded successfully")
    except Exception as e:
        logger.error(f"Error initializing Web pass generator: {e}")
    
    # Log available generators
    logger.info(
        f"Available pass generators: {', '.join(generators.keys()) or 'None'}"
    )
    
    return generators


def create_pass_service() -> PassGenerationService:
    """
    Create a pass generation service.
    
    Returns:
        PassGenerationService instance
    """
    # The service initializes its own generators
    return PassGenerationService()
