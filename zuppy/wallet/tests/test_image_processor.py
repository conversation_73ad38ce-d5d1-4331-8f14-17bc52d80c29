"""Tests for the wallet image processor module."""

from unittest import mock
from django.test import TestCase
from django.core.files.storage import default_storage
from zuppy.wallet.models import ImageConversionQueue, ProcessedImage
from zuppy.wallet.services.image_processor import process_image_queue


class ImageProcessorTests(TestCase):
    """Tests for the wallet image processor module."""

    def test_queue_template_images(self):
        """Test the queue_template_images function."""
        with mock.patch('zuppy.wallet.services.image_processor.process_image_queue') as mock_process:
            # Queue the template images
            queue_template_images(self.template.id)

            # Check that an ImageConversionQueue entry was created
            queue_items = ImageConversionQueue.objects.filter(template=self.template)
            self.assertEqual(queue_items.count(), 1)

            # Check that the process_image_queue task was called
            mock_process.delay.assert_called_once_with(queue_items.first().id)

    def test_process_image_queue(self):
        """Test the process_image_queue function."""
        # Create a queue item
        queue_item = ImageConversionQueue.objects.create(
            template=self.template,
            image_field='icon_image',
            source_path=self.template.icon_image.name,
            status='PENDING'
        )
        
        with mock.patch('zuppy.wallet.services.image_processor.process_for_platform') as mock_process:
            # Process the queue item
            process_image_queue(queue_item.id)
            
            # Refresh from DB
            queue_item.refresh_from_db()
            
            # Check that the status was updated to COMPLETED
            self.assertEqual(queue_item.status, 'COMPLETED')
            
            # Check that process_for_platform was called for each platform
            self.assertEqual(mock_process.call_count, 3)  # APPLE, GOOGLE, APPLE_WATCH
    
    def test_process_for_platform(self):
        """Test the process_for_platform function."""
        # Create a queue item
        queue_item = ImageConversionQueue.objects.create(
            template=self.template,
            image_field='icon_image',
            source_path=self.template.icon_image.name,
            status='PENDING'
        )

    @mock.patch('zuppy.wallet.services.image_processor.logger')
    @mock.patch('zuppy.wallet.services.image_processor.ProcessedImage')
    @mock.patch('zuppy.wallet.services.image_processor.ImageConversionQueue.objects.get')
    def test_handle_image_processing_error(
        self, mock_get_queue_item, mock_processed_image, mock_logger
    ):
        """Test that errors during image processing are handled gracefully."""
        # Setup test data
        queue_item = mock.MagicMock()
        queue_item.status = 'PENDING'
        queue_item.template_id = 1
        queue_item.image_type = 'icon_image'
        queue_item.error_message = ''
        mock_get_queue_item.return_value = queue_item
        
        # Mock the process_for_platform to raise an exception
        with mock.patch('zuppy.wallet.services.image_processor.process_for_platform') as mock_process:
            mock_process.side_effect = Exception('Test error')
            
            # Call the function that should handle the error
            process_image_queue(1)  # queue_id=1
            
            # Verify the queue item was updated to failed status
            self.assertEqual(queue_item.status, 'FAILED')
            self.assertIn('Test error', queue_item.error_message)
            queue_item.save.assert_called_once()
            
            # Verify error was logged
            mock_logger.error.assert_called_once()
            # Verify no ProcessedImage was created
            mock_processed_image.objects.create.assert_not_called()
            
            # After all retries, the task would be marked as FAILED by Celery
            # But we can't test that here as it's handled by Celery's retry mechanism

    def test_platform_sizes_configuration(self):
        """Test that platform sizes are correctly defined."""
        sizes = get_platform_sizes()
        
        # Check that all platforms are defined
        self.assertIn('APPLE', sizes)
        self.assertIn('GOOGLE', sizes)
        self.assertIn('APPLE_WATCH', sizes)
        
        # Check that Apple platform has all required image types
        apple_sizes = sizes['APPLE']
        required_types = ['icon', 'logo', 'strip', 'hero', 'thumbnail', 'footer']
        for req_type in required_types:
            self.assertIn(req_type, apple_sizes)
        
        # Check that Google platform has correct image types
        google_sizes = sizes['GOOGLE']
        google_types = ['icon', 'logo', 'strip', 'hero', 'background']
        for g_type in google_types:
            self.assertIn(g_type, google_sizes)
        
        # Check specific size requirements for Apple Wallet
        self.assertEqual(apple_sizes['icon']['1x'], (87, 87))
        self.assertEqual(apple_sizes['icon']['2x'], (174, 174))
        self.assertEqual(apple_sizes['icon']['3x'], (261, 261))
        
        # Check specific size requirements for Google Wallet
        self.assertEqual(google_sizes['icon']['1x'], (660, 660))
        
        # Verify Apple Watch sizes are smaller
        watch_sizes = sizes['APPLE_WATCH']
        self.assertEqual(watch_sizes['icon']['1x'], (48, 48))
        self.assertLess(watch_sizes['icon']['1x'][0], apple_sizes['icon']['1x'][0])
        
    def test_thumbnail_generation(self):
        """Test that thumbnails are generated correctly."""
        with mock.patch('zuppy.wallet.services.image_processor.default_storage') as mock_storage:
            mock_storage.save.return_value = 'test/path/image.png'
            mock_storage.exists.return_value = True
            
            # Create a large test image
            img = Image.new('RGB', (1000, 500), color='green')
            img_io = BytesIO()
            img.save(img_io, format='PNG')
            img_io.seek(0)
            
            # Process to thumbnail size
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_path = temp_file.name
                
                # Override the save method to save to our temp file
                def mock_save(path, content):
                    with open(temp_path, 'wb') as f:
                        f.write(content.read())
                    return path
                
                mock_storage.save.side_effect = mock_save
                
                # Process to thumbnail size (90x90)
                process_and_save_image(
                    img_io,
                    'test/path/thumbnail.png',
                    (90, 90)
                )
                
                # Open the result and check dimensions
                result_img = Image.open(temp_path)
                self.assertEqual(result_img.size, (90, 90))
                
                # O método save é chamado duas vezes (PNG/JPEG + WebP)
                self.assertEqual(mock_storage.save.call_count, 2)
                
                # Clean up
                os.unlink(temp_path)