"""
Serviço para download de arquivos .pkpass (Apple Wallet).

Este módulo oferece funcionalidades para gerar e servir 
arquivos .pkpass para download pelo usuário.
"""

import logging
import os
from typing import Dict, Optional, Tuple, Union

from django.http import HttpResponse

# Import models when needed to avoid circular imports
# from zuppy.loyalty.models import LoyaltyProgram, LoyaltyReward, LoyaltyUser, RewardRedemption
from zuppy.wallet.application.services.generate_pass import PassGenerationService

logger = logging.getLogger(__name__)


class DownloadPkpassService:
    """
    Serviço para download de arquivos .pkpass (Apple Wallet).
    
    Gerencia a criação e download de passes Apple Wallet a partir
    de modelos de cartão de fidelidade ou cupom.
    """
    
    def __init__(self):
        """Inicializa o serviço de download de passes."""
        try:
            self.pass_service = PassGenerationService()
        except Exception as e:
            logger.error(f"Erro ao inicializar PassGenerationService: {e}")
            self.pass_service = None
    
    def download_loyalty_card(
        self, 
        loyalty_user: 'LoyaltyUser', 
        pass_data: Optional[Dict] = None
    ) -> HttpResponse:
        """
        Note: LoyaltyUser is forward-referenced to avoid circular imports
        """
        # Import here to avoid circular imports
        from zuppy.loyalty.models import LoyaltyUser
        """
        Gera e prepara um cartão de fidelidade para download.
        
        Args:
            loyalty_user: Objeto LoyaltyUser para o qual gerar o passe
            pass_data: Dados adicionais para personalização do passe
            
        Returns:
            HttpResponse: Resposta HTTP com o arquivo .pkpass para download
            
        Raises:
            Exception: Se houver erro na geração do passe
        """
        try:
            # Verificar se o serviço está disponível
            if self.pass_service is None:
                raise ValueError("PassGenerationService não está disponível")
                
            # Importar o ApplePassGenerator diretamente
            from zuppy.wallet.infrastructure.apple.apple_pass_generator import ApplePassGenerator
            from zuppy.wallet.domain.entities.wallet_pass import StoreCardPass
            
            # Criar um applePassGenerator diretamente
            apple_generator = ApplePassGenerator()
            
            # Criar o passe
            loyalty_program = loyalty_user.program
            
            # Usar serial_number existente ou gerar um novo UUID
            import uuid
            from django.conf import settings
            
            serial_number = loyalty_user.serial_number or str(uuid.uuid4())
            
            # Obtém organizationName e passTypeIdentifier da configuração
            pass_type_identifier = settings.WALLET_SETTINGS.get(
                "APPLE_PASS_TYPE_IDENTIFIER", 
                "pass.com.zupy.clube.public"
            )
            organization_name = settings.WALLET_SETTINGS.get(
                "APPLE_ORGANIZATION_NAME", 
                loyalty_program.company.name
            )
            
            # Cria o objeto StoreCardPass
            wallet_pass = StoreCardPass(
                serial_number=serial_number,
                type_identifier=pass_type_identifier,
                organization_name=organization_name,
                description=f"Cartão de Fidelidade {loyalty_program.name}",
                logo_text=loyalty_program.logo_text or loyalty_program.name,
                foreground_color=loyalty_program.get_font_color(),
                background_color=loyalty_program.get_bg_color(),
                label_color=loyalty_program.get_header_color()
            )
            
            # Configura o código QR/barcode
            qr_value = pass_data.get('qr_value') if pass_data else None
            if not qr_value:
                qr_value = f"https://zupy.com/s/{loyalty_user.id}"
            
            wallet_pass.barcode_value = qr_value
            wallet_pass.barcode_format = pass_data.get('barcode_format', "PKBarcodeFormatQR")
            wallet_pass.barcode_alt_text = qr_value
            
            # Usar o gerador Apple corrigido que já tem a estrutura de campos
            # O apple_generator.generate() já cuidará da estrutura correta dos campos
            # usando o método _wallet_pass_to_card_data() e _create_storecard_fields()
            
            # Adicionar imagens - usando caminhos absolutos ou URLs com fallback
            # Logo image
            if hasattr(loyalty_program, 'logo') and loyalty_program.logo:
                try:
                    # Tentar caminho absoluto primeiro
                    if hasattr(loyalty_program.logo, 'path'):
                        try:
                            path = loyalty_program.logo.path
                            if os.path.exists(path):
                                wallet_pass.set_image("logo", path)
                                logger.debug(f"Using loyalty program logo path: {path}")
                                if hasattr(apple_generator, 'add_image'):
                                    apple_generator.add_image("logo", path)
                        except Exception as e:
                            logger.warning(f"Error setting logo path: {e}")
                    
                    # Se caminho não funcionou, tentar URL
                    if hasattr(loyalty_program.logo, 'url'):
                        image_url = loyalty_program.logo.url
                        wallet_pass.set_image("logo", image_url)
                        logger.debug(f"Using loyalty program logo URL: {image_url}")
                        if hasattr(apple_generator, 'add_image'):
                            apple_generator.add_image("logo", image_url)
                except Exception as e:
                    logger.error(f"Error setting loyalty program logo: {e}")
            
            # Icon image - critical for Apple Wallet
            if hasattr(loyalty_program, 'icon_image') and loyalty_program.icon_image:
                try:
                    # Tentar caminho absoluto primeiro
                    if hasattr(loyalty_program.icon_image, 'path'):
                        try:
                            path = loyalty_program.icon_image.path
                            if os.path.exists(path):
                                wallet_pass.set_image("icon", path)
                                logger.debug(f"Using loyalty program icon path: {path}")
                                if hasattr(apple_generator, 'add_image'):
                                    apple_generator.add_image("icon", path)
                        except Exception as e:
                            logger.warning(f"Error setting icon path: {e}")
                    
                    # Se caminho não funcionou, tentar URL
                    if hasattr(loyalty_program.icon_image, 'url'):
                        image_url = loyalty_program.icon_image.url
                        wallet_pass.set_image("icon", image_url)
                        logger.debug(f"Using loyalty program icon URL: {image_url}")
                        if hasattr(apple_generator, 'add_image'):
                            apple_generator.add_image("icon", image_url)
                except Exception as e:
                    logger.error(f"Error setting loyalty program icon: {e}")
            
            # Strip image
            if hasattr(loyalty_program, 'strip_image') and loyalty_program.strip_image:
                try:
                    # Tentar caminho absoluto primeiro
                    if hasattr(loyalty_program.strip_image, 'path'):
                        try:
                            path = loyalty_program.strip_image.path
                            if os.path.exists(path):
                                wallet_pass.set_image("strip", path)
                                logger.debug(f"Using loyalty program strip path: {path}")
                                if hasattr(apple_generator, 'add_image'):
                                    apple_generator.add_image("strip", path)
                        except Exception as e:
                            logger.warning(f"Error setting strip path: {e}")
                    
                    # Se caminho não funcionou, tentar URL
                    if hasattr(loyalty_program.strip_image, 'url'):
                        image_url = loyalty_program.strip_image.url
                        wallet_pass.set_image("strip", image_url)
                        logger.debug(f"Using loyalty program strip URL: {image_url}")
                        if hasattr(apple_generator, 'add_image'):
                            apple_generator.add_image("strip", image_url)
                except Exception as e:
                    logger.error(f"Error setting loyalty program strip: {e}")
            
            # Gerar o passe usando nosso gerador corrigido
            # Criar um dicionário com os dados do passe para usar nossa nova arquitetura
            card_data = {
                "customer_name": loyalty_user.user.get_full_name() or loyalty_user.user.username,
                "points_balance": loyalty_user.points_balance or 0,
                "points_name": loyalty_program.points_name or "Pontos",  # ✅ ADICIONADO
                "customer_label": loyalty_program.customer_label or "Cliente",  # ✅ ADICIONADO
                "tier_label": loyalty_program.tier_label or "Nível",  # ✅ ADICIONADO
                "loyalty_program": loyalty_program,
                "loyalty_user": loyalty_user,
                "tier": loyalty_user.tier,
                "qr_value": qr_value,
                "serial_number": serial_number,
                "pass_type_identifier": pass_type_identifier,
                "organization_name": organization_name,
                "description": f"Cartão de Fidelidade {loyalty_program.name}",
                "logo_text": loyalty_program.logo_text or loyalty_program.name,
                "foreground_color": loyalty_program.get_font_color(),
                "background_color": loyalty_program.get_bg_color(),
                "label_color": loyalty_program.get_header_color()
            }
            
            # Usar nosso método corrigido de geração
            pass_bytes = apple_generator._generate_pass_from_card_data(card_data)
            
            # Prepara a resposta HTTP
            response = HttpResponse(pass_bytes, content_type='application/vnd.apple.pkpass')
            
            # Get the card ID if available for consistent naming
            card_id = None
            if hasattr(loyalty_user, 'card') and loyalty_user.card:
                card_id = loyalty_user.card.id
            elif hasattr(loyalty_user, 'loyaltycard_set') and loyalty_user.loyaltycard_set.exists():
                card_id = loyalty_user.loyaltycard_set.first().id
            
            # Use card ID if available, otherwise use loyalty user ID
            if card_id:
                filename = f"card_{card_id}.pkpass"
            else:
                filename = f"loyalty_{loyalty_user.id}.pkpass"
            
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            
            # Atualiza o status de instalação (será marcado como instalado)
            loyalty_user.card_installed = True
            loyalty_user.save(update_fields=["card_installed"])
            
            return response
            
        except Exception as e:
            logger.error(f"Erro ao gerar cartão de fidelidade: {e}")
            raise
    
    def download_coupon_card(
        self, 
        redemption: 'RewardRedemption', 
        pass_data: Optional[Dict] = None
    ) -> HttpResponse:
        """
        Note: RewardRedemption is forward-referenced to avoid circular imports
        """
        # Import here to avoid circular imports
        from zuppy.loyalty.models import RewardRedemption
        """
        Gera e prepara um cupom para download.
        
        Args:
            redemption: Objeto RewardRedemption com dados do cupom
            pass_data: Dados adicionais para personalização do passe
            
        Returns:
            HttpResponse: Resposta HTTP com o arquivo .pkpass para download
            
        Raises:
            Exception: Se houver erro na geração do passe
        """
        try:
            # Configura dados extras do cupom
            coupon_data = pass_data or {}
            
            # Adiciona dados específicos do resgate
            if not coupon_data.get('coupon_code'):
                coupon_data['coupon_code'] = redemption.coupon_code
                
            if redemption.valid_until and not coupon_data.get('expiration_date'):
                coupon_data['expiration_date'] = redemption.valid_until
                
            # Configura o barcode/QR para o valor do cupom
            if not coupon_data.get('qr_value'):
                coupon_data['qr_value'] = f"https://zupy.com/c/{redemption.id}"
                
            # Usa o serial_number do resgate (se disponível)
            if not coupon_data.get('serial_number'):
                coupon_data['serial_number'] = str(redemption.id)
            
            # Verificar se o serviço está disponível
            if self.pass_service is None:
                raise ValueError("PassGenerationService não está disponível")
                
            # Implementação direta para cupons
            from zuppy.wallet.infrastructure.apple.apple_pass_generator import ApplePassGenerator
            from zuppy.wallet.domain.entities.wallet_pass import CouponPass
            import uuid
            from django.conf import settings
            from django.utils import timezone
            
            # Criar um ApplePassGenerator diretamente
            apple_generator = ApplePassGenerator()
            
            # Obter dados necessários
            loyalty_reward = redemption.reward
            loyalty_user = redemption.loyalty_user
            
            # Gera um novo UUID para o cupom se necessário
            serial_number = coupon_data.get('serial_number') if coupon_data else str(uuid.uuid4())
            
            # Obtém organizationName e passTypeIdentifier da configuração
            pass_type_identifier = settings.WALLET_SETTINGS.get(
                "APPLE_COUPON_TYPE_IDENTIFIER", 
                "pass.com.zupy.coupon"
            )
            organization_name = settings.WALLET_SETTINGS.get(
                "APPLE_ORGANIZATION_NAME", 
                loyalty_reward.program.company.name
            )
            
            # Determina a data de expiração
            if coupon_data and 'expiration_date' in coupon_data:
                expiration_date = coupon_data['expiration_date']
            elif loyalty_reward.validity_days:
                expiration_date = timezone.now() + timezone.timedelta(days=loyalty_reward.validity_days)
            else:
                expiration_date = timezone.now() + timezone.timedelta(days=30)  # Padrão: 30 dias
            
            # Cria o objeto CouponPass
            wallet_pass = CouponPass(
                serial_number=serial_number,
                type_identifier=pass_type_identifier,
                organization_name=organization_name,
                description=loyalty_reward.name,
                logo_text=loyalty_reward.program.logo_text or loyalty_reward.program.name,
                foreground_color=loyalty_reward.get_font_color(),
                background_color=loyalty_reward.get_bg_color(),
                label_color=loyalty_reward.get_header_color(),
                expiration_date=expiration_date
            )
            
            # Configura o código QR/barcode
            qr_value = coupon_data.get('qr_value') if coupon_data else None
            if not qr_value:
                qr_value = f"https://zupy.com/c/{serial_number}"
            
            wallet_pass.barcode_value = qr_value
            wallet_pass.barcode_format = coupon_data.get('barcode_format', "PKBarcodeFormatQR")
            wallet_pass.barcode_alt_text = qr_value
            
            # Configura o tipo de oferta no cabeçalho
            from zuppy.loyalty.models import LoyaltyReward
            
            offer_type = dict(LoyaltyReward.REWARD_TYPE_CHOICES).get(
                loyalty_reward.reward_type, 
                "Cupom"
            )
            wallet_pass.add_header_field(
                key="offer",
                label="Oferta",
                value=offer_type
            )
            
            # Campo principal com o título da oferta
            wallet_pass.add_primary_field(
                key="offer_title",
                label="Cupom",
                value=loyalty_reward.name
            )
            
            # Campos secundários com valor do desconto/benefício
            discount_value = coupon_data.get('discount_value') if coupon_data else None
            if not discount_value:
                if loyalty_reward.monetary_value:
                    discount_value = f"R$ {loyalty_reward.monetary_value:.2f}".replace(".", ",")
                else:
                    discount_value = f"{loyalty_reward.points_required} pontos"
            
            wallet_pass.add_secondary_field(
                key="discount",
                label="Valor",
                value=discount_value
            )
            
            # Adicionar imagens - usando caminhos absolutos ou URLs com fallback
            
            # Logo image - primeiro do reward, depois do programa
            if hasattr(loyalty_reward, 'logo_image') and loyalty_reward.logo_image:
                try:
                    # Tentar caminho absoluto primeiro
                    if hasattr(loyalty_reward.logo_image, 'path'):
                        try:
                            path = loyalty_reward.logo_image.path
                            if os.path.exists(path):
                                wallet_pass.set_image("logo", path)
                                logger.debug(f"Using reward logo path: {path}")
                                if hasattr(apple_generator, 'add_image'):
                                    apple_generator.add_image("logo", path)
                        except Exception as e:
                            logger.warning(f"Error setting reward logo path: {e}")
                    
                    # Se caminho não funcionou, tentar URL
                    if hasattr(loyalty_reward.logo_image, 'url'):
                        image_url = loyalty_reward.logo_image.url
                        wallet_pass.set_image("logo", image_url)
                        logger.debug(f"Using reward logo URL: {image_url}")
                        if hasattr(apple_generator, 'add_image'):
                            apple_generator.add_image("logo", image_url)
                except Exception as e:
                    logger.error(f"Error setting reward logo: {e}")
            # Fallback para o logo do programa
            elif hasattr(loyalty_reward.program, 'logo') and loyalty_reward.program.logo:
                try:
                    # Tentar caminho absoluto primeiro
                    if hasattr(loyalty_reward.program.logo, 'path'):
                        try:
                            path = loyalty_reward.program.logo.path
                            if os.path.exists(path):
                                wallet_pass.set_image("logo", path)
                                logger.debug(f"Using program logo path: {path}")
                                if hasattr(apple_generator, 'add_image'):
                                    apple_generator.add_image("logo", path)
                        except Exception as e:
                            logger.warning(f"Error setting program logo path: {e}")
                    
                    # Se caminho não funcionou, tentar URL
                    if hasattr(loyalty_reward.program.logo, 'url'):
                        image_url = loyalty_reward.program.logo.url
                        wallet_pass.set_image("logo", image_url)
                        logger.debug(f"Using program logo URL: {image_url}")
                        if hasattr(apple_generator, 'add_image'):
                            apple_generator.add_image("logo", image_url)
                except Exception as e:
                    logger.error(f"Error setting program logo: {e}")
            
            # Icon image - critical for Apple Wallet (tenta do reward, depois do programa)
            icon_set = False
            if hasattr(loyalty_reward, 'icon_image') and loyalty_reward.icon_image:
                try:
                    # Tentar caminho absoluto primeiro
                    if hasattr(loyalty_reward.icon_image, 'path'):
                        try:
                            path = loyalty_reward.icon_image.path
                            if os.path.exists(path):
                                wallet_pass.set_image("icon", path)
                                logger.debug(f"Using reward icon path: {path}")
                                if hasattr(apple_generator, 'add_image'):
                                    apple_generator.add_image("icon", path)
                                icon_set = True
                        except Exception as e:
                            logger.warning(f"Error setting reward icon path: {e}")
                    
                    # Se caminho não funcionou, tentar URL
                    if not icon_set and hasattr(loyalty_reward.icon_image, 'url'):
                        image_url = loyalty_reward.icon_image.url
                        wallet_pass.set_image("icon", image_url)
                        logger.debug(f"Using reward icon URL: {image_url}")
                        if hasattr(apple_generator, 'add_image'):
                            apple_generator.add_image("icon", image_url)
                        icon_set = True
                except Exception as e:
                    logger.error(f"Error setting reward icon: {e}")
            
            # Fallback para o ícone do programa se não tiver ícone próprio
            if not icon_set and hasattr(loyalty_reward.program, 'icon_image') and loyalty_reward.program.icon_image:
                try:
                    # Tentar caminho absoluto primeiro
                    if hasattr(loyalty_reward.program.icon_image, 'path'):
                        try:
                            path = loyalty_reward.program.icon_image.path
                            if os.path.exists(path):
                                wallet_pass.set_image("icon", path)
                                logger.debug(f"Using program icon path: {path}")
                                if hasattr(apple_generator, 'add_image'):
                                    apple_generator.add_image("icon", path)
                                icon_set = True
                        except Exception as e:
                            logger.warning(f"Error setting program icon path: {e}")
                    
                    # Se caminho não funcionou, tentar URL
                    if not icon_set and hasattr(loyalty_reward.program.icon_image, 'url'):
                        image_url = loyalty_reward.program.icon_image.url
                        wallet_pass.set_image("icon", image_url)
                        logger.debug(f"Using program icon URL: {image_url}")
                        if hasattr(apple_generator, 'add_image'):
                            apple_generator.add_image("icon", image_url)
                        icon_set = True
                except Exception as e:
                    logger.error(f"Error setting program icon: {e}")
            
            # Strip image - primeiro do reward, depois do programa
            if hasattr(loyalty_reward, 'strip_image') and loyalty_reward.strip_image:
                try:
                    # Tentar caminho absoluto primeiro
                    if hasattr(loyalty_reward.strip_image, 'path'):
                        try:
                            path = loyalty_reward.strip_image.path
                            if os.path.exists(path):
                                wallet_pass.set_image("strip", path)
                                logger.debug(f"Using reward strip path: {path}")
                                if hasattr(apple_generator, 'add_image'):
                                    apple_generator.add_image("strip", path)
                        except Exception as e:
                            logger.warning(f"Error setting reward strip path: {e}")
                    
                    # Se caminho não funcionou, tentar URL
                    if hasattr(loyalty_reward.strip_image, 'url'):
                        image_url = loyalty_reward.strip_image.url
                        wallet_pass.set_image("strip", image_url)
                        logger.debug(f"Using reward strip URL: {image_url}")
                        if hasattr(apple_generator, 'add_image'):
                            apple_generator.add_image("strip", image_url)
                except Exception as e:
                    logger.error(f"Error setting reward strip: {e}")
            # Fallback para a imagem strip do programa
            elif hasattr(loyalty_reward.program, 'strip_image') and loyalty_reward.program.strip_image:
                try:
                    # Tentar caminho absoluto primeiro
                    if hasattr(loyalty_reward.program.strip_image, 'path'):
                        try:
                            path = loyalty_reward.program.strip_image.path
                            if os.path.exists(path):
                                wallet_pass.set_image("strip", path)
                                logger.debug(f"Using program strip path: {path}")
                                if hasattr(apple_generator, 'add_image'):
                                    apple_generator.add_image("strip", path)
                        except Exception as e:
                            logger.warning(f"Error setting program strip path: {e}")
                    
                    # Se caminho não funcionou, tentar URL
                    if hasattr(loyalty_reward.program.strip_image, 'url'):
                        image_url = loyalty_reward.program.strip_image.url
                        wallet_pass.set_image("strip", image_url)
                        logger.debug(f"Using program strip URL: {image_url}")
                        if hasattr(apple_generator, 'add_image'):
                            apple_generator.add_image("strip", image_url)
                except Exception as e:
                    logger.error(f"Error setting program strip: {e}")
            
            # Para cupons, manter a geração tradicional por enquanto
            # TODO: Implementar estrutura DDD para cupons também
            pass_bytes = apple_generator.generate(wallet_pass)
            
            # Prepara a resposta HTTP
            response = HttpResponse(pass_bytes, content_type='application/vnd.apple.pkpass')
            
            # Get the card ID if available for consistent naming
            card_id = None
            if hasattr(redemption, 'wallet_card') and redemption.wallet_card:
                card_id = redemption.wallet_card.id
            
            # Use card ID if available, otherwise use redemption ID
            if card_id:
                filename = f"card_{card_id}.pkpass"
            else:
                filename = f"coupon_{redemption.id}.pkpass"
            
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            
            # Registra URL de carteira (se houver wallet_card associado)
            if redemption.wallet_card:
                redemption.wallet_card.apple_wallet_url = redemption.coupon_url
                redemption.wallet_card.save(update_fields=["apple_wallet_url"])
            
            return response
            
        except Exception as e:
            logger.error(f"Erro ao gerar cupom: {e}")
            raise
    
    def get_loyalty_user_from_id(self, card_id: str) -> Optional['LoyaltyUser']:
        """
        Note: LoyaltyUser is forward-referenced to avoid circular imports
        """
        # Import here to avoid circular imports
        from zuppy.loyalty.models import LoyaltyUser
        """
        Obtém um LoyaltyUser a partir do ID do cartão.
        
        Args:
            card_id: ID do cartão (pode ser serial_number ou ID do objeto)
            
        Returns:
            Optional[LoyaltyUser]: Objeto LoyaltyUser encontrado ou None
        """
        # Tenta encontrar por serial_number primeiro
        loyalty_user = LoyaltyUser.objects.filter(serial_number=card_id).first()
        
        # Se não encontrou, tenta pelo ID do objeto
        if not loyalty_user:
            loyalty_user = LoyaltyUser.objects.filter(id=card_id).first()
            
        return loyalty_user
    
    def get_redemption_from_id(self, card_id: str) -> Optional['RewardRedemption']:
        """
        Note: RewardRedemption is forward-referenced to avoid circular imports
        """
        # Import here to avoid circular imports
        from zuppy.loyalty.models import RewardRedemption
        """
        Obtém um RewardRedemption a partir do ID do cartão.
        
        Args:
            card_id: ID do cartão (pode ser ID do objeto ou serial_number)
            
        Returns:
            Optional[RewardRedemption]: Objeto RewardRedemption encontrado ou None
        """
        # Tenta encontrar pelo ID do objeto
        redemption = RewardRedemption.objects.filter(id=card_id).first()
            
        return redemption
    
    def download_pass_by_id(
        self, 
        card_id: str, 
        pass_type: str = "loyalty"
    ) -> Tuple[HttpResponse, str]:
        """
        Gera e prepara um passe para download a partir do ID.
        
        Args:
            card_id: ID do cartão (serial_number ou ID do objeto)
            pass_type: Tipo de passe (loyalty ou coupon)
            
        Returns:
            Tuple[HttpResponse, str]: Resposta HTTP e tipo de passe gerado
            
        Raises:
            ValueError: Se o cartão não for encontrado
            Exception: Se houver erro na geração do passe
        """
        if pass_type == "loyalty":
            # Procura LoyaltyUser
            loyalty_user = self.get_loyalty_user_from_id(card_id)
            
            if not loyalty_user:
                raise ValueError(f"Cartão de fidelidade não encontrado: {card_id}")
                
            # Gera e retorna o passe
            response = self.download_loyalty_card(loyalty_user)
            return response, "loyalty"
            
        elif pass_type == "coupon":
            # Procura RewardRedemption
            redemption = self.get_redemption_from_id(card_id)
            
            if not redemption:
                raise ValueError(f"Cupom não encontrado: {card_id}")
                
            # Gera e retorna o passe
            response = self.download_coupon_card(redemption)
            return response, "coupon"
            
        else:
            raise ValueError(f"Tipo de passe não suportado: {pass_type}")
    
    def download_pass_auto_detect(self, card_id: str) -> Tuple[HttpResponse, str]:
        """
        Detecta automaticamente o tipo de passe e gera para download.
        
        Args:
            card_id: ID do cartão (serial_number ou ID do objeto)
            
        Returns:
            Tuple[HttpResponse, str]: Resposta HTTP e tipo de passe gerado
            
        Raises:
            ValueError: Se o cartão não for encontrado
            Exception: Se houver erro na geração do passe
        """
        # Tenta encontrar como cupom primeiro
        redemption = self.get_redemption_from_id(card_id)
        
        if redemption:
            # É um cupom
            response = self.download_coupon_card(redemption)
            return response, "coupon"
        
        # Tenta encontrar como cartão de fidelidade
        loyalty_user = self.get_loyalty_user_from_id(card_id)
        
        if loyalty_user:
            # É um cartão de fidelidade
            response = self.download_loyalty_card(loyalty_user)
            return response, "loyalty"
        
        # Não encontrou nenhum tipo
        raise ValueError(f"Cartão não encontrado: {card_id}")