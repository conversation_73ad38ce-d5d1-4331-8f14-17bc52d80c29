"""Views for the wallet app."""

import base64
import io
import json
import logging
import uuid
from typing import Any

from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.files.storage import default_storage
from django.http import HttpRequest
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.views.generic import DetailView
from django.views.generic import ListView
from django.views.generic import TemplateView
from django.views.generic import View

from zuppy.wallet.models import CouponCard
from zuppy.wallet.models import LoyaltyCard
from zuppy.wallet.models import WalletCard

logger = logging.getLogger(__name__)

# Import QR Code generation functionality
try:
    import pyqrcode
except ImportError:
    pyqrcode = None


class LoyaltyCardListView(LoginRequiredMixin, ListView):
    """View that displays all loyalty cards for a user."""

    model = LoyaltyCard
    template_name = "wallet/loyalty_card_list.html"
    context_object_name = "cards"

    def get_queryset(self):
        """Return only active loyalty cards for the current user."""
        return LoyaltyCard.objects.filter(
            user=self.request.user,
            is_active=True,
        ).select_related("company", "loyalty_program", "loyalty_user")


class CouponCardListView(LoginRequiredMixin, ListView):
    """View that displays all coupon cards for a user."""

    model = CouponCard
    template_name = "wallet/coupon_card_list.html"
    context_object_name = "coupons"

    def get_queryset(self):
        """Return only active coupon cards for the current user."""
        return CouponCard.objects.filter(
            user=self.request.user,
            is_active=True,
        ).select_related("company", "reward")


class WalletHomeView(TemplateView):
    """View that displays the wallet home page."""

    template_name = "wallet/home.html"

    def get_context_data(self, **kwargs):
        """Add cards to context."""
        context = super().get_context_data(**kwargs)

        if self.request.user.is_authenticated:
            context["loyalty_cards"] = LoyaltyCard.objects.filter(
                user=self.request.user,
                is_active=True,
            ).select_related("company", "loyalty_program", "loyalty_user")

            context["coupon_cards"] = CouponCard.objects.filter(
                user=self.request.user,
                is_active=True,
            ).select_related("company", "reward")

        return context


class WebPassView(DetailView):
    """View that displays a web version of a pass."""

    model = WalletCard
    template_name = "wallet/web_pass.html"
    context_object_name = "pass"
    slug_field = "id"
    slug_url_kwarg = "card_id"

    def get_object(self, queryset=None):
        """Get the object and ensure it has a simplified barcode."""
        # Check if we need a custom lookup
        card_id = self.kwargs.get(self.slug_url_kwarg)
        if queryset is None:
            queryset = self.get_queryset()

        # Try multiple fields for lookup (ID, serial_number, or barcode_value)
        try:
            card = queryset.get(**{self.slug_field: card_id})
        except self.model.DoesNotExist:
            # Try by serial_number
            try:
                card = queryset.get(serial_number=card_id)
            except self.model.DoesNotExist:
                # Try by barcode_value
                try:
                    card = queryset.get(barcode_value=card_id)
                except self.model.DoesNotExist:
                    # Last attempt with a partial match on barcode_value
                    cards = queryset.filter(barcode_value__contains=card_id)
                    if cards.exists():
                        card = cards.first()
                    else:
                        raise self.model.DoesNotExist(
                            f"No card found with ID, serial_number or barcode_value matching '{card_id}'",
                        )

        # Continue with the card object

        # Verificar se o cartão tem um barcode_value no formato simplificado
        if hasattr(card, "barcode_value") and card.barcode_value:
            # Verificar se está no formato simplificado (PREFIX-UUID)
            is_simplified = (
                isinstance(card.barcode_value, str)
                and len(card.barcode_value) <= 12
                and "-" in card.barcode_value
                and len(card.barcode_value.split("-", 1)[0]) == 2
            )

            if not is_simplified:
                # Precisa simplificar o barcode
                logger.info(
                    f"Simplifying complex barcode for web pass card {card.id}: {card.barcode_value[:20]}...",
                )

                # Determinar o prefixo com base no tipo de cartão
                prefix_map = {
                    "coupon": "CP",
                    "storecard": "ST",
                    "loyalty": "ZP",
                    "generic": "ZP",
                    "event": "EV",
                }

                card_type = getattr(card, "pass_type", "loyalty").lower()
                prefix = prefix_map.get(card_type, "ZP")

                # Gerar ID único
                unique_id = uuid.uuid4().hex[:8].upper()

                # Criar barcode simplificado
                simplified_barcode = f"{prefix}-{unique_id}"

                # Atualizar o cartão
                card.barcode_value = simplified_barcode
                card.save(update_fields=["barcode_value"])
                logger.info(
                    f"Web pass card {card.id} barcode updated to: {simplified_barcode}",
                )

        return card


class WalletWebPassView(WebPassView):
    """View that displays a web version of a pass using serial number."""

    slug_field = "serial_number"
    slug_url_kwarg = "serial_number"

    # Inherits all the improved functionality from WebPassView for flexible lookup

    def generate_qr_code(self, value):
        """Generate a QR code as a base64 encoded PNG image."""
        if pyqrcode is None:
            return None
            
        # Criar o QR code usando pyqrcode
        qr = pyqrcode.create(value, error="L")

        # Salvar o QR code em formato PNG em um buffer
        buffer = io.BytesIO()
        qr.png(
            buffer,
            scale=10,
            module_color=[0, 0, 0, 255],
            background=[255, 255, 255, 255],
        )
        buffer.seek(0)

        # Converter para base64
        return base64.b64encode(buffer.getvalue()).decode("utf-8")

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add extra context data for the template."""
        context = super().get_context_data(**kwargs)

        # Check if we have a pre-generated QR code
        card = self.object
        file_path = f"passes/qr/web_pass_{card.id}.png"

        if default_storage.exists(file_path):
            context["qr_url"] = default_storage.url(file_path)
        elif hasattr(card, "barcode_value") and card.barcode_value:
            # Generate QR code on-the-fly if no pre-generated code is available
            qr_code_data = self.generate_qr_code(card.barcode_value)
            if qr_code_data:
                context["pass"]["qr_code_data"] = qr_code_data

        # Check if we have a pre-generated pass JSON
        json_path = f"passes/web/{card.id}.json"
        if default_storage.exists(json_path):
            try:
                with default_storage.open(json_path, "r") as f:
                    pass_data = json.load(f)

                # Update context with pass data
                for key, value in pass_data.items():
                    if key not in context:
                        context[key] = value
            except Exception as e:
                logger.error(f"Error loading web pass data: {e}")

        # Use WebP images if available (from processed images)
        try:
            # Find any associated program or reward
            from zuppy.wallet.models import ProcessedImage

            # Check if card has a template
            template = None
            if hasattr(card, "template") and card.template:
                template = card.template

            # Try to find associated processed images with WebP versions
            if template:
                # For each image type, try to find WebP version
                for image_type in ["logo_image", "icon_image", "strip_image"]:
                    # Skip if card already has this image
                    card_image_attr = image_type.replace("_image", "") if image_type != "logo_image" else "logo"
                    if card_image_attr in context["pass"] and context["pass"][card_image_attr]:
                        continue

                    # Find processed image with WebP version
                    processed_image = ProcessedImage.objects.filter(
                        conversion_queue__template=template,
                        conversion_queue__image_field=image_type,
                        webp_path__isnull=False,
                    ).first()

                    if processed_image and processed_image.webp_path:
                        if default_storage.exists(processed_image.webp_path):
                            # Add WebP URL to pass context
                            context["pass"][card_image_attr] = default_storage.url(
                                processed_image.webp_path,
                            )
                            logger.info(f"Using WebP image for {card_image_attr}")
        except Exception as e:
            logger.error(f"Error loading WebP images: {e}")

        return context


class AppleWalletPassView(View):
    """View that generates and serves an Apple Wallet pass file."""

    def get(self, request: HttpRequest, card_id: str) -> HttpResponse:
        """Generate and serve the .pkpass file."""
        try:
            # Get the card with select_related to load related models
            from zuppy.wallet.models import LoyaltyCard
            from zuppy.wallet.models import WalletCard

            # Try to get LoyaltyCard directly (most efficient)
            try:
                card = LoyaltyCard.objects.select_related(
                    "user",
                    "company",
                    "template",
                    "loyalty_program",
                    "loyalty_user",
                ).get(id=card_id)
                logger.info(f"Found loyalty card directly: {card_id}")
            except LoyaltyCard.DoesNotExist:
                # Fallback to WalletCard if not a LoyaltyCard
                card = get_object_or_404(WalletCard, id=card_id)
                logger.info(f"Fallback to WalletCard: {card_id}")

            logger.info(f"Directly generating Apple Wallet pass for card: {card_id}")

            # Use the configured Apple Pass Type Identifier from settings
            from django.conf import settings

            pass_type_identifier = settings.WALLET_SETTINGS.get(
                "APPLE_PASS_TYPE_IDENTIFIER",
                "pass.com.zupy.clube.public",
            )

            # Debug loyalty information
            if isinstance(card, LoyaltyCard):
                logger.info(f"Card {card_id} is a loyalty card")
                if hasattr(card, "loyalty_program"):
                    loyalty_program = card.loyalty_program
                    logger.info(
                        f"LoyaltyProgram: {loyalty_program.id} - {loyalty_program.name}",
                    )
                if hasattr(card, "loyalty_user"):
                    loyalty_user = card.loyalty_user
                    logger.info(
                        f"LoyaltyUser: {loyalty_user.id} - Points: {loyalty_user.points_balance}",
                    )

            # Import the domain model
            from zuppy.wallet.domain.entities.wallet_pass import StoreCardPass

            # Create a domain entity from the card data - using StoreCardPass for loyalty cards
            # Apple Wallet expects "storecard" not "loyalty" for loyalty/membership cards
            # Convert UUID to string explicitly to avoid serialization issues
            serial_str = str(card.serial_number) if card.serial_number else str(uuid.uuid4())

            # Get loyalty program information if available
            program = None
            if isinstance(card, LoyaltyCard) and hasattr(card, "loyalty_program"):
                program = card.loyalty_program

            # Use organization name from program if available
            organization_name = "ZUPY"
            if program and program.company and program.company.name:
                organization_name = program.company.name

            # Use description from program
            description = "Zupy Loyalty Card"
            if program:
                description = f"Cartão de Fidelidade {program.name}"
            elif card.description:
                description = card.description

            # Use logo_text from program or company
            logo_text = "ZUPY"
            if program and program.logo_text:
                logo_text = program.logo_text
            elif program and program.name:
                logo_text = program.name
            elif card.logo_text:
                logo_text = card.logo_text
            elif card.company and card.company.name:
                logo_text = card.company.name

            # Use colors from program if available
            foreground_color = card.foreground_color or "#000000"
            background_color = card.background_color or "#FFFFFF"
            label_color = card.label_color or "#555555"

            if program:
                # Try to use program's color getter methods if they exist
                if hasattr(program, "get_font_color") and callable(
                    program.get_font_color,
                ):
                    foreground_color = program.get_font_color() or foreground_color
                elif hasattr(program, "foreground_color"):
                    foreground_color = program.foreground_color or foreground_color

                if hasattr(program, "get_bg_color") and callable(program.get_bg_color):
                    background_color = program.get_bg_color() or background_color
                elif hasattr(program, "background_color"):
                    background_color = program.background_color or background_color

                if hasattr(program, "get_header_color") and callable(
                    program.get_header_color,
                ):
                    label_color = program.get_header_color() or label_color
                elif hasattr(program, "label_color"):
                    label_color = program.label_color or label_color

            # Create pass with program information (following Apple Wallet guidelines)
            wallet_pass = StoreCardPass(
                serial_number=serial_str,  # Using string version to avoid UUID serialization issues
                type_identifier=pass_type_identifier,
                organization_name=organization_name,
                description=description,
                logo_text=logo_text,
                foreground_color=str(foreground_color),
                background_color=str(background_color),
                label_color=str(label_color),
            )

            # Helper function to ensure JSON-safe values
            def safe_value(val):
                """Convert any non-JSON-serializable values to strings"""
                # Handle None values
                if val is None:
                    return ""
                # Handle UUID objects
                if hasattr(val, "__class__") and val.__class__.__name__ == "UUID":
                    return str(val)
                # Handle other objects that might not be JSON serializable
                if not isinstance(val, (str, int, float, bool, list, dict)):
                    return str(val)
                return val

            # --- CORREÇÃO CRÍTICA: USAR CONFIGURAÇÕES FIXAS CORRETAS ---

            # Força configurações corretas do arquivo funcional
            wallet_pass.set_metadata("webServiceURL", "https://zupy.com/wallet/passkit")

            # Gera authenticationToken no formato correto (UUID com hífens)
            auth_token = str(uuid.uuid4())
            wallet_pass.set_metadata("authenticationToken", auth_token)

            # --- FOLLOWING APPLE DESIGN SPECS FROM claude/tasks/apple_wallet_card_design.md ---

            # 1. Get points information from the LoyaltyUser directly for most accurate data
            points_value = None
            points_label = "ZUPCoins"
            change_message = "Novos pontos: %@"

            # First try to get points directly from LoyaltyUser (most accurate source)
            if (
                isinstance(card, LoyaltyCard)
                and hasattr(card, "loyalty_user")
                and card.loyalty_user is not None
            ):
                # Get points from loyalty_user
                loyalty_user = card.loyalty_user
                points_value = loyalty_user.points_balance

                # Get custom points label from the program if available
                if hasattr(card, "loyalty_program") and card.loyalty_program:
                    program = card.loyalty_program
                    if program.points_name:
                        points_label = program.points_name

                logger.info(
                    f"Using points directly from LoyaltyUser: {points_value} {points_label}",
                )

            # Fallback to card's points_balance field
            elif isinstance(card, LoyaltyCard) and hasattr(card, "points_balance"):
                points_value = card.points_balance
                logger.info(
                    f"Using points from card.points_balance: {points_value}",
                )

            # Fallback to primary fields
            elif card.primary_fields:
                # Search for points in primary fields
                for field in card.primary_fields:
                    if "point" in field.get("key", "").lower() or "point" in field.get("label", "").lower():
                        points_value = field.get("value")
                        if field.get("label"):
                            points_label = field.get("label")
                        logger.info(
                            f"Found points in primary field: {points_value} {points_label}",
                        )
                        break
                # If not found, just use the first primary field
                if points_value is None and len(card.primary_fields) > 0:
                    points_value = card.primary_fields[0].get("value")
                    if card.primary_fields[0].get("label"):
                        points_label = card.primary_fields[0].get("label")
                    logger.info(
                        f"Using first primary field as points: {points_value} {points_label}",
                    )

            # CORREÇÃO CRÍTICA: Usar pontos como campo primário (não vazio)
            if points_value is not None:
                # Create primary field for Apple Wallet - CRITICAL
                primary_field = {
                    "key": "loyalty-points",
                    "label": points_label.upper(),
                    "value": str(safe_value(points_value)),
                    "changeMessage": change_message,
                }
                wallet_pass.set_primary_fields([primary_field])
                
                # Também adicionar no header para melhor visibilidade
                header_field = {
                    "key": "balance",
                    "label": "SALDO",
                    "value": str(safe_value(points_value)),
                    "changeMessage": change_message,
                }
                wallet_pass.set_header_fields([header_field])
            else:
                # Default points if none found
                primary_field = {
                    "key": "loyalty-points",
                    "label": "PONTOS",
                    "value": "0",
                    "changeMessage": change_message,
                }
                wallet_pass.set_primary_fields([primary_field])
                
                header_field = {
                    "key": "balance",
                    "label": "SALDO",
                    "value": "0",
                    "changeMessage": change_message,
                }
                wallet_pass.set_header_fields([header_field])

            # 3. Add customer information to SECONDARY fields
            # Look for customer information in card
            customer_name = ""
            member_since = ""
            company_name = card.company.name if card.company else "ZUPY"

            # Get customer name from the card or user
            if hasattr(card, "user") and card.user:
                if hasattr(card.user, "name") and card.user.name:
                    customer_name = card.user.name
                elif hasattr(card.user, "get_full_name"):
                    customer_name = card.user.get_full_name()
                    # Get first name only if available
                    if " " in customer_name:
                        first_name = customer_name.split(" ")[0]
                        customer_name = first_name
                elif hasattr(card.user, "email"):
                    customer_name = card.user.email

            # Look in secondary fields for customer info if not found
            if not customer_name and card.secondary_fields:
                for field in card.secondary_fields:
                    if any(key in field.get("key", "").lower() for key in ["customer", "name", "client"]):
                        customer_name = field.get("value", "")
                        break

            # Get member since date
            if hasattr(card, "created_at"):
                # Format date as DD/MM/YYYY
                if card.created_at:
                    try:
                        member_since = card.created_at.strftime("%d/%m/%Y")
                    except Exception:
                        pass

            # Secondary fields array (customer info)
            secondary_fields = []

            # Add customer name to secondary fields - formatted as in design specs
            if customer_name:
                secondary_fields.append(
                    {
                        "key": "customer",
                        "label": company_name,
                        "value": customer_name,
                    },
                )

            # Set all secondary fields at once
            if secondary_fields:
                wallet_pass.set_secondary_fields(secondary_fields)

            # Look for tier/status in card data for auxiliary fields
            auxiliary_fields = []
            tier = None

            # Find tier information
            if isinstance(card, LoyaltyCard) and hasattr(card, "tier") and card.tier:
                tier = card.tier
            elif card.secondary_fields:
                for field in card.secondary_fields:
                    if "tier" in field.get("key", "").lower() or "status" in field.get("key", "").lower():
                        tier = field.get("value")
                        break

            # Add tier to auxiliary fields if found
            if tier:
                auxiliary_fields.append(
                    {
                        "key": "tier",
                        "label": "Nível",
                        "value": safe_value(tier),
                    },
                )

            # Set auxiliary fields if available
            if auxiliary_fields:
                wallet_pass.set_auxiliary_fields(auxiliary_fields)

            # 4. Set barcode data with proper formatting
            if card.barcode_value:
                wallet_pass.barcode_value = card.barcode_value
                wallet_pass.barcode_format = "PKBarcodeFormatQR"
                wallet_pass.barcode_alt_text = card.barcode_alt_text or card.barcode_value

            # 5. Add informative back fields following Apple Wallet design specs
            # Structure back fields in accordance with the design
            back_fields = []

            # Add default last message
            back_fields.append(
                {
                    "key": "loyalty-message",
                    "label": "Última Mensagem",
                    "value": "Bem-vindo ao programa de fidelidade!",
                    "changeMessage": "%@",
                },
            )

            # Add default profile if available
            if hasattr(card, "user") and card.user:
                user_name = card.user.get_full_name() if hasattr(card.user, "get_full_name") else ""
                user_email = card.user.email if hasattr(card.user, "email") else ""
                user_phone = str(card.user.phone) if hasattr(card.user, "phone") and card.user.phone else ""

                profile_value = f"{user_name}\n{user_email}\n{user_phone}"

                back_fields.append(
                    {
                        "key": "loyalty-profile",
                        "label": "Perfil",
                        "value": profile_value,
                    },
                )

            # Add default offers link
            program_url = "https://zupy.com/@nomedoprograma"
            if (
                isinstance(card, LoyaltyCard)
                and hasattr(card, "loyalty_program")
                and hasattr(card.loyalty_program, "slug")
            ):
                program_slug = card.loyalty_program.slug
                program_url = f"https://zupy.com/@{program_slug}"

            back_fields.append(
                {
                    "key": "loyalty-offers",
                    "label": "Prêmios",
                    "value": f"Prêmios Disponíveis: {program_url}",
                },
            )

            # Add contact/company information
            if card.company:
                company_name = card.company.name
                company_address = card.company.address if hasattr(card.company, "address") else ""
                company_phone = str(card.company.phone) if hasattr(card.company, "phone") and card.company.phone else ""
                company_email = card.company.email if hasattr(card.company, "email") else ""
                company_website = ""

                if hasattr(card.company, "website"):
                    company_website = card.company.website

                contact_value = f"{company_name}\n{company_address}\n\n{company_phone}\n{company_email}"
                if company_website:
                    contact_value += f"\n{company_website}"

                back_fields.append(
                    {
                        "key": "loyalty-contact",
                        "label": "Contato",
                        "value": contact_value,
                    },
                )

            # Add default terms
            if isinstance(card, LoyaltyCard) and hasattr(card, "loyalty_program"):
                program = card.loyalty_program
                terms_text = "Termos e condições do programa de fidelidade."

                if hasattr(program, "terms_and_conditions") and program.terms_and_conditions:
                    terms_text = program.terms_and_conditions

                back_fields.append(
                    {
                        "key": "loyalty-terms",
                        "label": "Termos",
                        "value": terms_text,
                    },
                )

            # Adicionar member since e número do cartão ao verso do cartão
            if member_since:
                back_fields.append(
                    {
                        "key": "member-since",
                        "label": "Membro Desde",
                        "value": member_since,
                    },
                )

            # Adicionar número do cartão se disponível
            if isinstance(card, LoyaltyCard) and hasattr(card, "card_number") and card.card_number:
                back_fields.append(
                    {
                        "key": "cardNumber",
                        "label": "Nº do Cartão",
                        "value": card.card_number,
                    },
                )

            # Set all back fields at once
            if back_fields:
                wallet_pass.set_back_fields(back_fields)

            # Load pass generator
            from zuppy.wallet.infrastructure.apple.apple_pass_generator import ApplePassGenerator

            # Create pass generator
            generator = ApplePassGenerator()

            # Log what we've built so we can debug
            logger.info("Creating Apple Wallet pass with following data:")
            logger.info(f"- Card ID: {card.id}")
            logger.info(f"- Serial: {serial_str}")
            logger.info(f"- Points: {points_value} (label: {points_label})")
            logger.info(f"- Customer: {customer_name}")
            logger.info(f"- Member since: {member_since}")
            if isinstance(card, LoyaltyCard) and hasattr(card, "card_number"):
                logger.info(f"- Card number: {card.card_number}")
            if hasattr(card, "barcode_value") and card.barcode_value:
                logger.info(f"- Barcode: {card.barcode_value}")

            # Generate the pass
            pass_file = generator.generate(wallet_pass)

            # Return the file download with proper headers for Safari
            # O Safari no iOS tem requisitos específicos para arquivos .pkpass
            response = HttpResponse(
                pass_file,
                content_type="application/vnd.apple.pkpass",
            )

            # Cabeçalhos críticos para Safari - aplicar em ordem e formato exatos
            response["Content-Type"] = "application/vnd.apple.pkpass"  # Reforçar MIME type
            response["Content-Length"] = str(len(pass_file))  # Tamanho exato do arquivo como string
            # O Safari e iOS requerem formato específico para Content-Disposition
            # IMPORTANTE: Formato correto com aspas duplas e attachment
            response["Content-Disposition"] = f'attachment; filename="{serial_str}.pkpass"'

            # Headers adicionais específicos para pkpass
            response["X-Apple-Wallet-Compatibility"] = "iOS"
            response["X-Apple-Assetd-Bucket-Id"] = "com.apple.pkpass"

            # Desativar caching para evitar problemas
            response["Cache-Control"] = "no-cache, no-store, must-revalidate, private"
            response["Pragma"] = "no-cache"
            response["Expires"] = "0"

            # Permitir acesso cross-origin para dispositivos iOS
            response["Access-Control-Allow-Origin"] = "*"
            response["Access-Control-Allow-Methods"] = "GET"
            response["Access-Control-Allow-Headers"] = "Content-Type"

            logger.info(
                f"Successfully generated Apple Wallet pass for card: {card.id} with Safari-compatible headers",
            )
            return response

        except Exception as e:
            logger.exception(f"Error in AppleWalletPassView: {e}")

            # Last attempt - try to show web pass view
            try:
                # Get the card again (in case previous attempts failed)
                from zuppy.wallet.models import WalletCard

                card = get_object_or_404(WalletCard, id=card_id)

                # Render a simple web pass manually
                from django.shortcuts import render

                context = {
                    "card": card,
                    "title": card.title,
                    "description": card.description,
                    "barcode_value": card.barcode_value,
                    "error_message": "Unable to generate Apple Wallet pass. Showing web version instead.",
                }
                return render(request, "wallet/web_pass.html", context)

            except Exception as web_error:
                logger.exception(f"Error showing web pass: {web_error}")
                
                # Last resort fallback
                from django.http import HttpResponseServerError
                return HttpResponseServerError(
                    "Error generating wallet pass: " + str(e),
                )


class GoogleWalletPassView(View):
    """View that generates and redirects to Google Wallet pass."""

    def get(self, request: HttpRequest, card_id: str) -> HttpResponse:
        """Generate and redirect to Google Wallet pass."""
        # Import from infrastructure views where it's properly implemented
        from zuppy.wallet.infrastructure.views.wallet_views import GoogleWalletPassView as InfraGoogleWalletPassView
        view = InfraGoogleWalletPassView()
        return view.get(request, card_id)


# Template Management Views
class WalletTemplateListView(LoginRequiredMixin, ListView):
    """View for listing wallet templates."""

    model = None  # Will be set when WalletTemplate is imported
    template_name = "wallet/template_list.html"
    context_object_name = "templates"

    def get_queryset(self):
        """Return templates for the current user's company."""
        from zuppy.wallet.models import WalletTemplate
        if not hasattr(self, 'model') or self.model is None:
            self.model = WalletTemplate
        
        queryset = self.model.objects.all()
        if hasattr(self.request.user, 'company') and self.request.user.company:
            queryset = queryset.filter(company=self.request.user.company)
        return queryset.select_related('company')


class WalletTemplateDetailView(LoginRequiredMixin, DetailView):
    """View for displaying wallet template details."""

    template_name = "wallet/template_detail.html"
    context_object_name = "template"

    def get_object(self, queryset=None):
        """Get template by ID."""
        from zuppy.wallet.models import WalletTemplate
        return get_object_or_404(WalletTemplate, id=self.kwargs['pk'])


class WalletTemplateCreateView(LoginRequiredMixin, TemplateView):
    """View for creating wallet templates."""

    template_name = "wallet/template_create.html"

    def get_context_data(self, **kwargs):
        """Add context for template creation."""
        context = super().get_context_data(**kwargs)
        context['title'] = 'Create Wallet Template'
        return context


class WalletTemplateUpdateView(LoginRequiredMixin, TemplateView):
    """View for updating wallet templates."""

    template_name = "wallet/template_edit.html"

    def get_context_data(self, **kwargs):
        """Add context for template editing."""
        context = super().get_context_data(**kwargs)
        from zuppy.wallet.models import WalletTemplate
        template = get_object_or_404(WalletTemplate, id=self.kwargs['pk'])
        context['template'] = template
        context['title'] = f'Edit Template: {template.name}'
        return context


class WalletTemplateDeleteView(LoginRequiredMixin, TemplateView):
    """View for deleting wallet templates."""

    template_name = "wallet/template_delete.html"

    def get_context_data(self, **kwargs):
        """Add context for template deletion."""
        context = super().get_context_data(**kwargs)
        from zuppy.wallet.models import WalletTemplate
        template = get_object_or_404(WalletTemplate, id=self.kwargs['pk'])
        context['template'] = template
        context['title'] = f'Delete Template: {template.name}'
        return context


class WalletTemplatePreviewView(LoginRequiredMixin, TemplateView):
    """View for previewing wallet templates."""

    template_name = "wallet/template_preview.html"

    def get_context_data(self, **kwargs):
        """Add context for template preview."""
        context = super().get_context_data(**kwargs)
        from zuppy.wallet.models import WalletTemplate
        template = get_object_or_404(WalletTemplate, id=self.kwargs['pk'])
        context['template'] = template
        context['title'] = f'Preview Template: {template.name}'
        return context


# Geofence Management Views
class GeofencePointCreateView(LoginRequiredMixin, TemplateView):
    """View for creating geofence points."""

    template_name = "wallet/geofence_create.html"

    def get_context_data(self, **kwargs):
        """Add context for geofence creation."""
        context = super().get_context_data(**kwargs)
        from zuppy.wallet.models import WalletTemplate
        template = get_object_or_404(WalletTemplate, id=self.kwargs['template_pk'])
        context['template'] = template
        context['title'] = f'Add Geofence to {template.name}'
        return context


class GeofencePointDeleteView(LoginRequiredMixin, TemplateView):
    """View for deleting geofence points."""

    template_name = "wallet/geofence_delete.html"

    def get_context_data(self, **kwargs):
        """Add context for geofence deletion."""
        context = super().get_context_data(**kwargs)
        # Note: GeofencePoint model would need to be imported when it exists
        context['title'] = 'Delete Geofence Point'
        return context


# Scanner Views
class ScanQRCodeView(LoginRequiredMixin, TemplateView):
    """View for scanning QR codes."""

    template_name = "wallet/scan_qr.html"

    def get_context_data(self, **kwargs):
        """Add context for QR scanning."""
        context = super().get_context_data(**kwargs)
        context['title'] = 'Scan QR Code'
        return context


class ScanLogListView(LoginRequiredMixin, ListView):
    """View for listing scan logs."""

    template_name = "wallet/scan_logs.html"
    context_object_name = "logs"

    def get_queryset(self):
        """Return scan logs."""
        # For now, return empty queryset until ScanLog model is defined
        from django.db import models
        return models.QuerySet().none()

    def get_context_data(self, **kwargs):
        """Add context for scan logs."""
        context = super().get_context_data(**kwargs)
        context['title'] = 'Scan Logs'
        return context
