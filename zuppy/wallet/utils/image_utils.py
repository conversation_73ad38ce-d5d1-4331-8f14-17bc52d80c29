"""
Utility functions for image processing and manipulation.

This module provides functions for loading, processing, and validating
images for different wallet platforms.
"""

import base64
import io
import logging
import os
from typing import Dict, Optional, Tuple

from django.conf import settings
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage

logger = logging.getLogger(__name__)


def ensure_image_exists(image_path: str) -> bool:
    """
    Check if an image exists at the given path.
    
    Args:
        image_path: Path to the image file
        
    Returns:
        True if the image exists, False otherwise
    """
    if not image_path:
        return False
        
    # Handle both local paths and storage URLs
    if image_path.startswith(('http://', 'https://')):
        # For URLs, just assume they exist
        return True
    else:
        # For local paths, check if file exists
        return os.path.exists(image_path) or default_storage.exists(image_path)


def get_default_images() -> Dict[str, str]:
    """
    Get paths to default images for wallet passes.
    
    Returns:
        Dictionary with paths to default images for each image type
    """
    # Get image paths from settings or use defaults
    wallet_settings = getattr(settings, 'WALLET_SETTINGS', {})
    
    # Define default paths with fallbacks to existing images
    default_images = {
        'icon': wallet_settings.get('DEFAULT_ICON_PATH'),
        'logo': wallet_settings.get('DEFAULT_LOGO_PATH'),
        'strip': wallet_settings.get('DEFAULT_STRIP_PATH'),
    }
    
    # Use existing images as fallbacks
    static_root = getattr(settings, 'STATIC_ROOT', '/app/staticfiles')
    fallback_images = {
        'icon': os.path.join(static_root, 'images/ico-zupy.png'),
        'logo': os.path.join(static_root, 'images/logo-zupy.png'),
        'strip': os.path.join(static_root, 'images/zupy-google-wallet-default-hero-840x312.png'),
    }
    
    # Check if default images exist, otherwise use fallbacks
    for image_type in ['icon', 'logo', 'strip']:
        if not default_images[image_type] or not ensure_image_exists(default_images[image_type]):
            fallback_path = fallback_images[image_type]
            if ensure_image_exists(fallback_path):
                default_images[image_type] = fallback_path
                logger.info(f"Using fallback image for {image_type}: {fallback_path}")
            else:
                logger.warning(f"No valid image found for {image_type}")
                default_images[image_type] = None
    
    return default_images


def base64_to_file(base64_data: str, file_path: str, prefix: str = 'data:image/png;base64,') -> str:
    """
    Convert base64 image data to a file.
    
    Args:
        base64_data: Base64-encoded image data
        file_path: Path where the file should be saved
        prefix: Base64 prefix to remove (if present)
        
    Returns:
        Path to the saved file
    """
    # Remove prefix if present
    if base64_data.startswith(prefix):
        base64_data = base64_data[len(prefix):]
        
    # Decode base64 data
    file_data = base64.b64decode(base64_data)
    
    # Save to storage
    return default_storage.save(file_path, ContentFile(file_data))


def create_default_icon_image() -> Tuple[bytes, str]:
    """
    Create a default icon image for wallet passes.
    
    Returns:
        Tuple of (image_data, file_name)
    """
    # Default Zupy icon
    icon_data = base64.b64decode(
        "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAMAAADXqc3KAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAFdQTFRFAAAA/RsM/h8Q/z8//yIU/x8Q/hoL/xwN/xsM/xwN/hkK/h0P/hkK/x8Q/yQW/yYY/x0O/xsM/x4P/x0O/x8Q/xwN/h4P/iAR/hsM/hwN/h0O/////SnZEQAAAAd0Uk5TADM9TVZYzv5mhwGbAAAAuUlEQVQoz3WSWRLDIAxDDRiCE+Ilafn/P+0BktA25VgejQYbCPNi3CL6fC9kPIDj/lJjdzuA/gcIJbYrQN8A+VIXwI/A3wpIq1ZIPQrc2GkToF3JMVS9IWqbkH3Iij1IPWA8EeTeBTQfBFDFULd0M0Ct2tI8CXuq9jCVQAYC5qkE5AQ+UXdACPV1SQcKAdJUnaWgQCdvt9ONlp+to/lj5xCd2WLNlu7XGdP1bXUzPjZX99HrbnX44/UbXAMPLvLsOT0AAAAASUVORK5CYII="
    )
    
    return icon_data, 'icon.png'
