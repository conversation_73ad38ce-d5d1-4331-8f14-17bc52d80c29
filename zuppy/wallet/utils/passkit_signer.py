"""
Módulo para gerar e assinar arquivos .pkpass para Apple Wallet.

Este módulo contém funções para criar e assinar corretamente arquivos .pkpass
conforme a especificação da Apple, usando settings do Django e subprocess para
interagir com o OpenSSL.
"""

import hashlib
import json
import logging
import os
import subprocess
import tempfile
import zipfile
from pathlib import Path

from django.conf import settings

logger = logging.getLogger(__name__)


def get_wallet_settings() -> dict[str, str]:
    """
    Obtém as configurações necessárias para Apple Wallet a partir do Django settings.

    Returns:
        Dict[str, str]: Dicionário com as configurações para Apple Wallet

    Raises:
        ValueError: Se alguma configuração obrigatória estiver faltando
    """
    # Certifique-se de que settings.WALLET_SETTINGS existe
    wallet_settings = getattr(settings, "WALLET_SETTINGS", {})

    # Obtém as configurações com fallback para os settings diretos (compatibilidade)
    config = {
        "pass_type_identifier": wallet_settings.get("APPLE_PASS_TYPE_IDENTIFIER")
        or getattr(settings, "APPLE_PASS_TYPE_IDENTIFIER", None),
        "team_identifier": wallet_settings.get("APPLE_TEAM_IDENTIFIER")
        or getattr(settings, "APPLE_TEAM_IDENTIFIER", None),
        "organization_name": wallet_settings.get("APPLE_ORGANIZATION_NAME")
        or getattr(settings, "APPLE_ORGANIZATION_NAME", None),
        "certificate_path": wallet_settings.get("APPLE_CERTIFICATE_PATH")
        or getattr(settings, "APPLE_CERTIFICATE_PATH", None),
        "certificate_password": wallet_settings.get("APPLE_CERTIFICATE_PASSWORD")
        or getattr(settings, "APPLE_CERTIFICATE_PASSWORD", None),
        "wwdr_certificate_path": wallet_settings.get("APPLE_WWDR_CERTIFICATE_PATH")
        or getattr(settings, "APPLE_WWDR_CERTIFICATE_PATH", None),
    }

    # Verifica se todas as configurações obrigatórias estão presentes
    missing = [k for k, v in config.items() if v is None]
    if missing:
        raise ValueError(
            f"Configurações de Apple Wallet faltando: {', '.join(missing)}",
        )

    # Tenta usar o certificado WWDR G4 mais recente se disponível
    if config["wwdr_certificate_path"]:
        wwdr_g4_path = (
            Path(config["wwdr_certificate_path"]).parent / "AppleWWDRCAG4.pem"
        )
        if os.path.exists(wwdr_g4_path):
            logger.info(f"Usando certificado WWDR G4 mais recente: {wwdr_g4_path}")
            config["wwdr_certificate_path"] = str(wwdr_g4_path)

    return config


def create_manifest_json(pass_dir: str | Path) -> Path:
    """
    Cria o arquivo manifest.json com hashes SHA-1 de todos os arquivos no diretório.

    Args:
        pass_dir: Diretório contendo os arquivos do passe

    Returns:
        Path: Caminho para o arquivo manifest.json criado
    """
    pass_dir = Path(pass_dir)
    manifest = {}

    # Itera por todos os arquivos no diretório, exceto manifest.json e signature
    for file_path in pass_dir.glob("*"):
        if file_path.is_file() and file_path.name not in ["manifest.json", "signature"]:
            # Calcula o hash SHA-1 do arquivo
            file_hash = hashlib.sha1()
            file_hash.update(file_path.read_bytes())
            manifest[file_path.name] = file_hash.hexdigest()

    # Caminho para o manifest.json
    manifest_path = pass_dir / "manifest.json"

    # Escreve o arquivo JSON
    with open(manifest_path, "w", encoding="utf-8") as f:
        json.dump(manifest, f, indent=2)

    return manifest_path


def sign_pass(
    pass_dir: str | Path,
    certificate_path: str | None = None,
    certificate_password: str | None = None,
    wwdr_path: str | None = None,
) -> None:
    """
    Assina um diretório .pkpass com os certificados específicos da Apple.

    Args:
        pass_dir: Diretório contendo os arquivos do passe
        certificate_path: Caminho para o certificado .p12
        certificate_password: Senha do certificado
        wwdr_path: Caminho para o certificado WWDR da Apple

    Raises:
        ValueError: Se houver erro na leitura dos certificados ou na assinatura
        subprocess.CalledProcessError: Se houver erro nos comandos OpenSSL
    """
    pass_dir = Path(pass_dir)

    # Obtém as configurações (se não forem fornecidas explicitamente)
    if certificate_path is None or certificate_password is None or wwdr_path is None:
        config = get_wallet_settings()
        certificate_path = certificate_path or config["certificate_path"]
        certificate_password = certificate_password or config["certificate_password"]
        wwdr_path = wwdr_path or config["wwdr_certificate_path"]

    # Verifica se os arquivos existem
    if not os.path.exists(certificate_path):
        raise ValueError(f"Arquivo de certificado não encontrado: {certificate_path}")
    if not os.path.exists(wwdr_path):
        raise ValueError(f"Arquivo de certificado WWDR não encontrado: {wwdr_path}")

    # Cria o arquivo manifest.json se não existir
    manifest_path = pass_dir / "manifest.json"
    if not manifest_path.exists():
        create_manifest_json(pass_dir)

    # Cria diretório temporário para extrair os certificados
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_dir = Path(temp_dir)
        cert_pem = temp_dir / "cert.pem"
        key_pem = temp_dir / "key.pem"

        # 1. Extrai o certificado do arquivo .p12
        logger.info(f"Extraindo certificado de {certificate_path}")
        subprocess.run(
            [
                "openssl",
                "pkcs12",
                "-in",
                certificate_path,
                "-clcerts",
                "-nokeys",
                "-out",
                cert_pem,
                "-passin",
                f"pass:{certificate_password}",
            ],
            check=True,
            capture_output=True,
        )

        # 2. Extrai a chave privada do arquivo .p12
        logger.info(f"Extraindo chave privada de {certificate_path}")
        subprocess.run(
            [
                "openssl",
                "pkcs12",
                "-in",
                certificate_path,
                "-nocerts",
                "-out",
                key_pem,
                "-nodes",
                "-passin",
                f"pass:{certificate_password}",
            ],
            check=True,
            capture_output=True,
        )

        # 3. Assina o manifest.json
        signature_path = pass_dir / "signature"
        logger.info(f"Assinando manifest.json em {pass_dir}")
        subprocess.run(
            [
                "openssl",
                "smime",
                "-binary",
                "-sign",
                "-signer",
                cert_pem,
                "-inkey",
                key_pem,
                "-certfile",
                wwdr_path,
                "-in",
                manifest_path,
                "-out",
                signature_path,
                "-outform",
                "DER",
            ],
            check=True,
            capture_output=True,
        )

        logger.info(f"Arquivo signature criado em {signature_path}")


def create_pkpass(pass_dir: str | Path, output_path: str | Path | None = None) -> Path:
    """
    Cria um arquivo .pkpass a partir de um diretório de passe.

    Args:
        pass_dir: Diretório contendo os arquivos do passe
        output_path: Caminho de saída para o arquivo .pkpass

    Returns:
        Path: Caminho para o arquivo .pkpass criado

    Raises:
        ValueError: Se houver erro na criação do arquivo .pkpass
    """
    pass_dir = Path(pass_dir)

    # Gera um caminho de saída se não for fornecido
    if output_path is None:
        output_path = pass_dir.parent / f"{pass_dir.name}.pkpass"
    else:
        output_path = Path(output_path)

    # Garante que manifest.json e signature existem antes de zipar
    manifest_path = pass_dir / "manifest.json"
    if not manifest_path.exists():
        create_manifest_json(pass_dir)
    signature_path = pass_dir / "signature"
    if not signature_path.exists():
        sign_pass(pass_dir)

    # Abre um arquivo ZIP para escrita
    try:
        with zipfile.ZipFile(output_path, "w", zipfile.ZIP_DEFLATED) as zip_file:
            # Primeiro adiciona todos os arquivos normais
            for file_path in pass_dir.iterdir():
                if file_path.is_file():
                    zip_file.write(file_path, arcname=file_path.name)
                    logger.info(f"Adicionado arquivo ao ZIP: {file_path.name}")

            # Depois adiciona diretórios .lproj e seus conteúdos
            lproj_folders = [
                f
                for f in pass_dir.iterdir()
                if f.is_dir() and f.name.endswith(".lproj")
            ]
            for lproj_dir in lproj_folders:
                # Adiciona cada arquivo dentro da pasta .lproj
                for file_path in lproj_dir.iterdir():
                    if file_path.is_file():
                        # Mantém a estrutura de diretórios no ZIP
                        arcname = f"{lproj_dir.name}/{file_path.name}"
                        zip_file.write(file_path, arcname=arcname)
                        logger.info(
                            f"Adicionado arquivo de localização ao ZIP: {arcname}",
                        )

        # Verifica o conteúdo do arquivo ZIP para diagnóstico
        with zipfile.ZipFile(output_path, "r") as zip_file:
            file_list = zip_file.namelist()
            logger.info(f"Conteúdo do arquivo .pkpass: {len(file_list)} arquivos")
            lproj_files = [f for f in file_list if ".lproj/" in f]
            logger.info(f"Arquivos de localização no .pkpass: {lproj_files}")

        logger.info(f"Arquivo .pkpass criado com sucesso: {output_path}")
        return output_path

    except Exception as e:
        logger.error(f"Erro ao criar arquivo .pkpass: {e}")
        raise ValueError(f"Erro ao criar arquivo .pkpass: {e}")


def assinar_pkpass(pasta_pkpass: str | Path, nome_saida: str | None = None) -> Path:
    """
    Assina um diretório de passe e cria um arquivo .pkpass assinado.

    Args:
        pasta_pkpass: Diretório contendo os arquivos do passe
        nome_saida: Nome do arquivo .pkpass de saída (opcional)

    Returns:
        Path: Caminho para o arquivo .pkpass assinado

    Raises:
        FileNotFoundError: Se o diretório não existir
        ValueError: Se houver erro na assinatura ou criação do arquivo
    """
    pasta_pkpass = Path(pasta_pkpass)

    # Verifica se o diretório existe
    if not pasta_pkpass.exists():
        raise FileNotFoundError(f"Pasta {pasta_pkpass} não existe")

    # Verifica se é um diretório
    if not pasta_pkpass.is_dir():
        raise ValueError(f"{pasta_pkpass} não é um diretório")

    # Verifica se pass.json existe
    pass_json = pasta_pkpass / "pass.json"
    if not pass_json.exists():
        raise FileNotFoundError(f"Arquivo pass.json não encontrado em {pasta_pkpass}")

    # Gera o caminho de saída
    if nome_saida is None:
        output_path = pasta_pkpass.parent / f"{pasta_pkpass.name}.pkpass"
    else:
        output_path = pasta_pkpass.parent / nome_saida

    # Cria o manifest.json
    create_manifest_json(pasta_pkpass)

    # Assina o passe
    sign_pass(pasta_pkpass)

    # Cria o arquivo .pkpass
    return create_pkpass(pasta_pkpass, output_path)
