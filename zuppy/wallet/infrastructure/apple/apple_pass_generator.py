"""
Apple Wallet pass generator implementation - usando implementação interna.

Esta implementação usa apenas as bibliotecas já disponíveis no sistema
e a implementação interna passkit_signer.py para gerar passes Apple Wallet.
"""

import json
import logging
import os
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, BinaryIO

from django.conf import settings

from zuppy.wallet.application.interfaces.pass_generator import PassGenerator
from zuppy.wallet.domain.entities.wallet_pass import WalletPass
from zuppy.wallet.utils.passkit_signer import create_pkpass, get_wallet_settings

logger = logging.getLogger(__name__)


class ApplePassGenerator(PassGenerator):
    """
    Apple Wallet pass generator usando implementação interna.
    
    Esta implementação usa apenas as bibliotecas já disponíveis:
    - cryptography, pyOpenSSL para certificados
    - Pillow para processamento de imagens
    - passkit_signer.py para assinatura
    """

    def __init__(self):
        """Initialize the Apple Wallet generator with configuration."""
        self._available = False
        
        try:
            # Get wallet settings using internal function
            self.config = get_wallet_settings()
            self._available = True
            logger.info("Apple Wallet generator initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Apple Wallet generator: {e}")
            self.config = {}

    def is_available(self) -> bool:
        """Check if this generator is available for use."""
        return self._available

    def get_platform(self) -> str:
        """Return the platform identifier."""
        return "APPLE"

    def generate_pass(self, card_data: dict, save_file: bool = True) -> Optional[Tuple[bytes, str]]:
        """
        Generate an Apple Wallet pass.
        
        Args:
            card_data: Dictionary with pass data
            save_file: Whether to save the pass file
            
        Returns:
            Tuple (pass_bytes, filename) or None if generation fails
        """
        if not self.is_available():
            logger.error("Apple Wallet generator not available")
            return None
            
        # Validate required fields
        required_fields = ["serial_number", "description", "barcode_value"]
        for field in required_fields:
            if not card_data.get(field):
                logger.error(f"Missing required field: {field}")
                return None
                
        try:
            # Create temporary directory for pass generation
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Create pass.json
                pass_json = self._create_pass_json(card_data)
                pass_json_path = temp_path / "pass.json"
                
                with open(pass_json_path, "w", encoding="utf-8") as f:
                    json.dump(pass_json, f, ensure_ascii=False, indent=2)
                    
                # Add images to pass directory
                self._add_images_to_pass(temp_path, card_data)
                
                # Generate filename
                pass_type = card_data.get("pass_type", "storeCard").lower()
                serial = str(card_data["serial_number"])
                filename = f"{pass_type}-{serial}.pkpass"
                
                # Create and sign the .pkpass file
                logger.info(f"Generating Apple Wallet pass: {filename}")
                
                pkpass_path = create_pkpass(temp_path)
                
                # Read the generated .pkpass file
                with open(pkpass_path, "rb") as f:
                    pass_data = f.read()
                    
                logger.info(f"Apple Wallet pass generated successfully: {filename}")
                return pass_data, filename
                
        except Exception as e:
            logger.exception(f"Error generating Apple Wallet pass: {e}")
            return None

    def _create_pass_json(self, card_data: dict) -> dict:
        """
        Create the pass.json structure for Apple Wallet.
        
        Args:
            card_data: Dictionary with pass data
            
        Returns:
            Dictionary with pass.json structure
        """
        # Base pass structure
        pass_json = {
            "formatVersion": 1,
            "passTypeIdentifier": self.config["pass_type_identifier"],
            "teamIdentifier": self.config["team_identifier"],
            "organizationName": self.config["organization_name"],
            "serialNumber": str(card_data["serial_number"]),
            "description": card_data["description"],
        }
        
        # Add logo text if provided
        if card_data.get("logo_text"):
            pass_json["logoText"] = card_data["logo_text"]
        elif card_data.get("company_name"):
            pass_json["logoText"] = card_data["company_name"]
            
        # Add colors if provided
        if card_data.get("background_color"):
            pass_json["backgroundColor"] = card_data["background_color"]
        if card_data.get("foreground_color"):
            pass_json["foregroundColor"] = card_data["foreground_color"]
        if card_data.get("label_color"):
            pass_json["labelColor"] = card_data["label_color"]
            
        # Configure barcode with correct encoding
        barcode = {
            "format": "PKBarcodeFormatQR",
            "message": card_data["barcode_value"],
            "messageEncoding": "utf-8",  # Usar UTF-8 para compatibilidade
            "altText": card_data.get("barcode_alt_text", card_data["barcode_value"])
        }
        pass_json["barcode"] = barcode
        pass_json["barcodes"] = [barcode]
        
        # Configure webservice for push updates
        site_url = card_data.get("site_url", settings.WALLET_SETTINGS.get("SITE_URL", ""))
        if site_url:
            if not site_url.endswith("/"):
                site_url += "/"
            pass_json["webServiceURL"] = f"{site_url}wallet/passkit"
            
            # Generate 16-character authentication token
            auth_token = card_data.get("authentication_token")
            if not auth_token or len(auth_token) != 16:
                import secrets
                auth_token = secrets.token_hex(8)  # Generates exactly 16 hex characters
                
            pass_json["authenticationToken"] = auth_token
            
        # Configure associated apps
        if card_data.get("app_store_id"):
            pass_json["associatedStoreIdentifiers"] = [int(card_data["app_store_id"])]
            
        # Configure locations if provided
        if card_data.get("locations"):
            pass_json["locations"] = card_data["locations"]
            
        # Determine pass type and configure fields
        pass_type = card_data.get("pass_type", "storeCard").lower()
        
        if pass_type == "coupon":
            pass_json["coupon"] = self._create_coupon_fields(card_data)
        else:
            # Default to storeCard for loyalty programs
            pass_json["storeCard"] = self._create_storecard_fields(card_data)
            
        return pass_json

    def _create_storecard_fields(self, card_data: dict) -> dict:
        """Create fields structure for store card (loyalty program) - Nova estrutura otimizada."""
        
        fields_structure = {
            "headerFields": [],
            "primaryFields": [],
            "secondaryFields": [],
            "auxiliaryFields": [],
            "backFields": []
        }
        
        # HEADER FIELDS - Pontos totalmente à direita (nome + saldo)
        points_name = card_data.get("points_name", "PONTOS")
        points_balance = card_data.get("points_balance", "0")
        
        if points_balance is not None:
            # Um único campo com nome dos pontos e saldo, alinhado à direita
            fields_structure["headerFields"].append({
                "key": "points",
                "label": points_name,
                "value": str(points_balance),
                "textAlignment": "PKTextAlignmentRight",
                "changeMessage": f"{points_name} atualizados: %@"
            })
            
        # PRIMARY FIELDS - Vazio para dar destaque à imagem strip
        # Não adicionamos nada aqui para manter apenas a imagem strip
        
        # SECONDARY FIELDS - Nome do cliente à esquerda
        if card_data.get("customer_name"):
            customer_label = card_data.get("customer_label", "Cliente")  # ✅ USAR LABEL CORRETO
            fields_structure["secondaryFields"].append({
                "key": "customer_name",
                "label": customer_label,
                "value": card_data["customer_name"],
                "textAlignment": "PKTextAlignmentLeft"
            })
            
        # Add custom fields if provided (mantém compatibilidade)
        self._add_custom_fields_to_structure(fields_structure, card_data)
        
        # BACK FIELDS - Informações completas do verso
        self._add_enhanced_back_fields(fields_structure, card_data)
        
        # Remove empty field arrays
        return {k: v for k, v in fields_structure.items() if v}

    def _create_coupon_fields(self, card_data: dict) -> dict:
        """Create fields structure for coupon."""
        
        fields_structure = {
            "headerFields": [],
            "primaryFields": [],
            "secondaryFields": [],
            "auxiliaryFields": [],
            "backFields": []
        }
        
        # Header fields - expiration date
        if card_data.get("expiration_date"):
            fields_structure["headerFields"].append({
                "key": "expires",
                "label": "EXPIRA EM",
                "value": card_data["expiration_date"]
            })
            
        # Primary fields - offer title
        if card_data.get("title"):
            fields_structure["primaryFields"].append({
                "key": "offer",
                "label": card_data.get("title_label", "OFERTA"),
                "value": card_data["title"]
            })
            
        # Secondary fields - discount value
        if card_data.get("discount_value"):
            fields_structure["secondaryFields"].append({
                "key": "discount",
                "label": "Desconto",
                "value": card_data["discount_value"]
            })
            
        # Add custom fields if provided
        self._add_custom_fields_to_structure(fields_structure, card_data)
        
        # Back fields
        self._add_back_fields_to_structure(fields_structure, card_data)
        
        # Remove empty field arrays
        return {k: v for k, v in fields_structure.items() if v}

    def _add_custom_fields_to_structure(self, fields_structure: dict, card_data: dict):
        """Add custom fields from card_data to the fields structure."""
        
        field_mappings = {
            "header_fields": "headerFields",
            "primary_fields": "primaryFields", 
            "secondary_fields": "secondaryFields",
            "auxiliary_fields": "auxiliaryFields",
        }
        
        for field_key, structure_key in field_mappings.items():
            if field_key in card_data and isinstance(card_data[field_key], list):
                for field in card_data[field_key]:
                    if field.get("label") and field.get("value"):
                        field_dict = {
                            "key": field.get("key", field["label"].lower().replace(" ", "_")),
                            "label": field["label"],
                            "value": field["value"]
                        }
                        fields_structure[structure_key].append(field_dict)

    def _add_enhanced_back_fields(self, fields_structure: dict, card_data: dict):
        """Add enhanced back fields with program info, contact, and links."""
        
        # 1. REGRAS DO PROGRAMA
        program_rules = card_data.get("program_rules") or card_data.get("terms")
        if program_rules:
            fields_structure["backFields"].append({
                "key": "program_rules",
                "label": "Regras do Programa", 
                "value": program_rules
            })
        elif card_data.get("loyalty_program_id"):
            # Buscar regras do programa no banco
            try:
                from zuppy.loyalty.models import LoyaltyProgram
                program = LoyaltyProgram.objects.get(id=card_data["loyalty_program_id"])
                if program.terms_and_conditions:  # ✅ CAMPO CORRETO
                    fields_structure["backFields"].append({
                        "key": "program_rules",
                        "label": "Regras do Programa",
                        "value": program.terms_and_conditions
                    })
            except Exception as e:
                logger.warning(f"Erro ao buscar regras do programa: {e}")
        
        # 2. INFORMAÇÕES DE CONTATO
        contact_info = self._build_contact_info(card_data)
        if contact_info:
            fields_structure["backFields"].append({
                "key": "contact_info",
                "label": "Informações de Contato",
                "value": contact_info
            })
        
        # 3. ÚLTIMA MENSAGEM (se houver)
        last_message = card_data.get("last_message")
        if last_message:
            fields_structure["backFields"].append({
                "key": "last_message",
                "label": "Última Mensagem",
                "value": last_message
            })
        
        # 4. LINK DO PROGRAMA (formato: https://zupy.com/@empresa)
        program_link = self._build_program_link(card_data)
        if program_link:
            fields_structure["backFields"].append({
                "key": "program_link", 
                "label": "Link do Programa",
                "value": program_link
            })
            
        # Custom back fields (mantém compatibilidade)
        if "back_fields" in card_data and isinstance(card_data["back_fields"], list):
            for field in card_data["back_fields"]:
                if field.get("label") and field.get("value"):
                    fields_structure["backFields"].append({
                        "key": field.get("key", field["label"].lower().replace(" ", "_")),
                        "label": field["label"],
                        "value": field["value"]
                    })

    def _build_contact_info(self, card_data: dict) -> str:
        """Build contact information string."""
        contact_parts = []
        
        # Nome da empresa
        if card_data.get("company_name"):
            contact_parts.append(card_data["company_name"])
        elif card_data.get("organization_name"):
            contact_parts.append(card_data["organization_name"])
        
        # Telefone
        if card_data.get("company_phone"):
            contact_parts.append(f"Tel: {card_data['company_phone']}")
        
        # Email
        if card_data.get("company_email"):
            contact_parts.append(f"Email: {card_data['company_email']}")
        
        # Website
        if card_data.get("company_website"):
            contact_parts.append(f"Site: {card_data['company_website']}")
        
        return "\n".join(contact_parts) if contact_parts else ""

    def _build_program_link(self, card_data: dict) -> str:
        """Build program link in format https://zupy.com/@empresa."""
        
        # Tentar obter slug do programa
        program_slug = None
        
        if card_data.get("loyalty_program_id"):
            try:
                from zuppy.loyalty.models import LoyaltyProgram
                program = LoyaltyProgram.objects.get(id=card_data["loyalty_program_id"])
                if program.slug:
                    program_slug = program.slug
            except Exception as e:
                logger.warning(f"Erro ao buscar slug do programa: {e}")
        
        # Fallback para slug da empresa
        if not program_slug and card_data.get("company_slug"):
            program_slug = card_data["company_slug"]
        
        # Retornar link formatado
        if program_slug:
            return f"https://zupy.com/@{program_slug}"
        
        return "https://zupy.com"

    def _add_back_fields_to_structure(self, fields_structure: dict, card_data: dict):
        """Legacy method - redirects to enhanced version for compatibility."""
        self._add_enhanced_back_fields(fields_structure, card_data)

    def _add_images_to_pass(self, pass_dir: Path, card_data: dict):
        """Add images to the pass directory with proper Apple specifications."""
        
        try:
            from PIL import Image
            
            # Get image paths
            icon_path, logo_path, strip_path = self._get_image_paths(card_data)
            
            # Process and add icon (required: 58x58, 116x116, 174x174)
            if icon_path and os.path.exists(icon_path):
                self._add_resized_images(pass_dir, icon_path, "icon", [
                    (58, ""), (116, "@2x"), (174, "@3x")
                ])
                
            # Process and add logo (160x50, 320x100, 480x150)
            if logo_path and os.path.exists(logo_path):
                self._add_resized_images(pass_dir, logo_path, "logo", [
                    (160, ""), (320, "@2x"), (480, "@3x")
                ], aspect_ratio=3.2)
                
            # Process and add strip (320x123, 640x246, 960x369) 
            if strip_path and os.path.exists(strip_path):
                self._add_resized_images(pass_dir, strip_path, "strip", [
                    (320, ""), (640, "@2x"), (960, "@3x")
                ], aspect_ratio=2.6)
                
        except ImportError:
            logger.warning("PIL not available, using images as-is")
            # Fallback to direct file copying
            icon_path, logo_path, strip_path = self._get_image_paths(card_data)
            
            if icon_path and os.path.exists(icon_path):
                import shutil
                shutil.copy2(icon_path, pass_dir / "icon.png")
                    
            if logo_path and os.path.exists(logo_path):
                import shutil
                shutil.copy2(logo_path, pass_dir / "logo.png")
                    
            if strip_path and os.path.exists(strip_path):
                import shutil
                shutil.copy2(strip_path, pass_dir / "strip.png")

    def _add_resized_images(self, pass_dir: Path, image_path: str, prefix: str, sizes: list, aspect_ratio: float = 1.0):
        """Add resized images to the pass directory."""
        
        from PIL import Image
        
        try:
            with Image.open(image_path) as img:
                for width, suffix in sizes:
                    height = int(width / aspect_ratio) if aspect_ratio != 1.0 else width
                    
                    resized_img = img.resize((width, height), Image.Resampling.LANCZOS)
                    
                    # Save as PNG
                    filename = f"{prefix}{suffix}.png"
                    resized_img.save(pass_dir / filename, format="PNG", optimize=True)
                    
                logger.info(f"Added {len(sizes)} resized {prefix} images to pass")
                
        except Exception as e:
            logger.error(f"Error processing {prefix} image: {e}")
            # Fallback to copying original file
            import shutil
            shutil.copy2(image_path, pass_dir / f"{prefix}.png")

    def _get_image_paths(self, card_data: dict) -> tuple[str | None, str | None, str | None]:
        """
        Busca imagens seguindo PRIORIDADE CORRETA:
        1. LoyaltyProgram (fonte principal)
        2. Imagens padrão (fallback final)
        
        WalletTemplate é apenas para layout, NÃO para imagens!
        """
        from zuppy.loyalty.models import LoyaltyProgram
        from zuppy.wallet.utils.image_utils import get_default_images

        icon_path = None
        logo_path = None
        strip_path = None
        
        # PRIORIDADE 1: LOYALTY PROGRAM (fonte principal de tudo)
        loyalty_program_id = card_data.get("loyalty_program_id")
        if loyalty_program_id:
            try:
                program = LoyaltyProgram.objects.get(id=loyalty_program_id)
                logger.info(f"Buscando imagens do programa: {program.name}")
                
                # Icon: programa.icon_image > programa.logo
                if hasattr(program, "icon_image") and program.icon_image:
                    try:
                        # Tentar path primeiro (local), depois URL (produção)
                        icon_path = program.icon_image.path if hasattr(program.icon_image, "path") else self._download_image_temp(program.icon_image.url)
                        logger.info(f"✅ Icon do programa: {icon_path}")
                    except (NotImplementedError, AttributeError):
                        # Fallback para URL em produção
                        icon_path = self._download_image_temp(program.icon_image.url)
                        logger.info(f"✅ Icon do programa (via URL): {icon_path}")
                elif hasattr(program, "logo") and program.logo:
                    try:
                        icon_path = program.logo.path if hasattr(program.logo, "path") else self._download_image_temp(program.logo.url)
                        logger.info(f"✅ Icon via logo do programa: {icon_path}")
                    except (NotImplementedError, AttributeError):
                        icon_path = self._download_image_temp(program.logo.url)
                        logger.info(f"✅ Icon via logo do programa (via URL): {icon_path}")
                
                # Logo: programa.logo
                if hasattr(program, "logo") and program.logo:
                    try:
                        logo_path = program.logo.path if hasattr(program.logo, "path") else self._download_image_temp(program.logo.url)
                        logger.info(f"✅ Logo do programa: {logo_path}")
                    except (NotImplementedError, AttributeError):
                        logo_path = self._download_image_temp(program.logo.url)
                        logger.info(f"✅ Logo do programa (via URL): {logo_path}")
                
                # Strip: programa.strip_image > programa.card_image
                if hasattr(program, "strip_image") and program.strip_image:
                    try:
                        strip_path = program.strip_image.path if hasattr(program.strip_image, "path") else self._download_image_temp(program.strip_image.url)
                        logger.info(f"✅ Strip do programa: {strip_path}")
                    except (NotImplementedError, AttributeError):
                        strip_path = self._download_image_temp(program.strip_image.url)
                        logger.info(f"✅ Strip do programa (via URL): {strip_path}")
                elif hasattr(program, "card_image") and program.card_image:
                    try:
                        strip_path = program.card_image.path if hasattr(program.card_image, "path") else self._download_image_temp(program.card_image.url)
                        logger.info(f"✅ Strip via card_image do programa: {strip_path}")
                    except (NotImplementedError, AttributeError):
                        strip_path = self._download_image_temp(program.card_image.url)
                        logger.info(f"✅ Strip via card_image do programa (via URL): {strip_path}")
                        
            except Exception as e:
                logger.error(f"Erro ao buscar programa {loyalty_program_id}: {e}")
        
        # PRIORIDADE 2: IMAGENS PADRÃO (fallback final)
        default_images = get_default_images()
        
        if not icon_path:
            icon_path = default_images.get("icon")
            logger.warning(f"Usando icon padrão: {icon_path}")
            
        if not logo_path:
            logo_path = default_images.get("logo")
            logger.warning(f"Usando logo padrão: {logo_path}")
            
        if not strip_path:
            strip_path = default_images.get("strip")
            logger.warning(f"Usando strip padrão: {strip_path}")
            
        logger.info(f"Imagens finais - Icon: {bool(icon_path)}, Logo: {bool(logo_path)}, Strip: {bool(strip_path)}")
        return icon_path, logo_path, strip_path

        """Get paths to images for the pass."""
        
        from zuppy.wallet.utils.image_utils import get_default_images
        
        icon_path = None
        logo_path = None  
        strip_path = None
        
        # Priority 1: Template images
        template_id = card_data.get("template_id")
        if template_id:
            try:
                from zuppy.wallet.models import ProcessedImage
                
                processed_images = ProcessedImage.objects.filter(
                    conversion_queue__template_id=template_id,
                    platform="APPLE",
                )
                
                for img in processed_images:
                    image_type = img.conversion_queue.image_field.replace("_image", "")
                    
                    if hasattr(img.processed_file, "path") and os.path.exists(img.processed_file.path):
                        image_path = img.processed_file.path
                    elif hasattr(img.processed_file, "url"):
                        # Download from URL if needed
                        image_path = self._download_image_temp(img.processed_file.url)
                    else:
                        continue
                        
                    if image_type == "icon":
                        icon_path = image_path
                    elif image_type == "logo":
                        logo_path = image_path
                    elif image_type == "strip":
                        strip_path = image_path
                        
            except Exception as e:
                logger.error(f"Error getting template images: {e}")
                
        # Priority 2: Loyalty program images
        loyalty_program_id = card_data.get("loyalty_program_id")
        if loyalty_program_id:
            try:
                from zuppy.loyalty.models import LoyaltyProgram
                
                program = LoyaltyProgram.objects.get(id=loyalty_program_id)
                
                if not icon_path and hasattr(program, "icon_image") and program.icon_image:
                    icon_path = self._get_image_path(program.icon_image)
                    
                if not logo_path and hasattr(program, "logo") and program.logo:
                    logo_path = self._get_image_path(program.logo)
                    
                if not strip_path and hasattr(program, "strip_image") and program.strip_image:
                    strip_path = self._get_image_path(program.strip_image)
                    
            except Exception as e:
                logger.error(f"Error getting loyalty program images: {e}")
                
        # Priority 3: Card data paths/URLs
        if not icon_path:
            icon_path = card_data.get("icon_path") or self._download_image_temp(card_data.get("icon_url"))
            
        if not logo_path:
            logo_path = card_data.get("logo_path") or self._download_image_temp(card_data.get("logo_url"))
            
        if not strip_path:
            strip_path = card_data.get("strip_path") or self._download_image_temp(card_data.get("strip_url"))
            
        # Priority 4: Company images
        company_id = card_data.get("company_id")
        if company_id and not logo_path:
            try:
                from zuppy.company.models import Company
                
                company = Company.objects.get(id=company_id)
                if hasattr(company, "logo") and company.logo:
                    logo_path = self._get_image_path(company.logo)
                    
            except Exception as e:
                logger.error(f"Error getting company images: {e}")
                
        # Priority 5: Default images
        default_images = get_default_images()
        
        if not icon_path:
            icon_path = default_images.get("icon")
            
        if not logo_path:
            logo_path = default_images.get("logo")
            
        if not strip_path:
            strip_path = default_images.get("strip")
            
        return icon_path, logo_path, strip_path

    def _get_image_path(self, image_field) -> Optional[str]:
        """Get path from Django ImageField, downloading if necessary."""
        
        if hasattr(image_field, "path") and os.path.exists(image_field.path):
            return image_field.path
        elif hasattr(image_field, "url"):
            return self._download_image_temp(image_field.url)
        return None

    def _download_image_temp(self, url: Optional[str]) -> Optional[str]:
        """Download image from URL to temporary file."""
        
        if not url:
            return None
            
        try:
            import urllib.request
            
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".png")
            urllib.request.urlretrieve(url, temp_file.name)
            temp_file.close()
            
            logger.debug(f"Downloaded image from URL to: {temp_file.name}")
            return temp_file.name
            
        except Exception as e:
            logger.error(f"Error downloading image from URL {url}: {e}")
            return None

    # Métodos da interface PassGenerator
    
    def generate(self, wallet_pass: WalletPass) -> bytes:
        """
        Gera um arquivo de passe a partir de um objeto WalletPass.
        """
        # Converter WalletPass para card_data format
        card_data = self._wallet_pass_to_card_data(wallet_pass)
        
        result = self.generate_pass(card_data, save_file=False)
        if result:
            pass_data, _ = result
            return pass_data
        else:
            raise Exception("Failed to generate Apple Wallet pass")
    
    def add_image(self, image_type: str, image_data: Union[bytes, BinaryIO, str]) -> bool:
        """
        Adiciona uma imagem ao passe.
        """
        try:
            # For this implementation, images are handled during pass generation
            # This method could be used to cache images for later use
            logger.info(f"Image {image_type} registered for addition")
            return True
        except Exception as e:
            logger.error(f"Error adding image {image_type}: {e}")
            return False
    
    def supports_pass_type(self, pass_type: str) -> bool:
        """
        Verifica se este gerador suporta o tipo de passe especificado.
        """
        supported_types = ["storecard", "coupon", "loyalty"]
        return pass_type.lower() in supported_types
    
    def get_supported_image_types(self) -> List[str]:
        """
        Retorna a lista de tipos de imagem suportados por este gerador.
        """
        return ["icon", "logo", "strip", "thumbnail"]
    
    def get_supported_barcode_formats(self) -> List[str]:
        """
        Retorna a lista de formatos de código de barras suportados.
        """
        return ["PKBarcodeFormatQR", "PKBarcodeFormatPDF417", "PKBarcodeFormatAztec", "PKBarcodeFormatCode128"]
    
    def validate_pass(self, wallet_pass: WalletPass) -> Dict[str, List[str]]:
        """
        Valida se o passe está corretamente configurado para este gerador.
        """
        errors = []
        warnings = []
        
        # Validar campos obrigatórios
        if not wallet_pass.serial_number:
            errors.append("Serial number is required")
        if not wallet_pass.description:
            errors.append("Description is required")
        if not wallet_pass.barcode_value:
            errors.append("Barcode value is required")
            
        # Validar configuração Apple Wallet
        if not self.is_available():
            errors.append("Apple Wallet configuration is not available")
            
        return {"errors": errors, "warnings": warnings}
    
    def generate_download_url(self, wallet_pass: WalletPass) -> str:
        """
        Gera uma URL para download do passe.
        """
        from django.urls import reverse
        from django.conf import settings
        
        site_url = getattr(settings, 'SITE_URL', 'https://zuppy.com.br')
        url_path = reverse('wallet:download_pass', kwargs={'pass_id': wallet_pass.id})
        return f"{site_url}{url_path}"
    
    def update_pass(self, wallet_pass: WalletPass) -> bytes:
        """
        Atualiza um passe existente.
        """
        # Para Apple Wallet, atualizações são feitas gerando um novo pass
        return self.generate(wallet_pass)
    
    def _wallet_pass_to_card_data(self, wallet_pass: WalletPass) -> dict:
        """
        Converte WalletPass para card_data PRIORIZANDO LOYALTY PROGRAM como fonte principal.
        """
        card_data = {
            "serial_number": wallet_pass.serial_number,
            "description": wallet_pass.description,
            "barcode_value": wallet_pass.barcode_value,
            "barcode_alt_text": getattr(wallet_pass, "barcode_alt_text", ""),
            "pass_type": getattr(wallet_pass, "pass_type", "storecard"),
        }
        
        # FONTE PRINCIPAL: LOYALTY PROGRAM (tudo vem daqui)
        program = getattr(wallet_pass, "loyalty_program", None)
        if program:
            logger.info(f"Carregando dados do programa: {program.name}")
            
            # Dados básicos do programa
            card_data.update({
                "loyalty_program_id": program.id,
                "points_name": program.points_name,  # ✅ AQUI está o "Pérolas"
                "program_name": program.name,
                "program_slug": program.slug,
                "logo_text": getattr(program, "logo_text", program.name),
            })
            
            # Regras e descrições do programa
            card_data.update({
                "program_rules": program.terms_and_conditions,  # ✅ AQUI estão as regras
                "description": program.description,
                "welcome_message": getattr(program, "welcome_message", ""),
            })
            
            # CORES DO PROGRAMA (substituem qualquer padrão)
            card_data.update({
                "background_color": program.background_color,  # ✅ CORES CORRETAS
                "foreground_color": program.foreground_color,
                "label_color": program.label_color,
            })
            
            # Dados da empresa via programa
            if program.company:
                company = program.company
                card_data.update({
                    "company_name": company.name,
                    "company_phone": getattr(company, "phone", ""),
                    "company_email": getattr(company, "email", ""),
                    "company_website": getattr(company, "website", ""),
                    "organization_name": company.name,  # Para compatibilidade
                })
        
        # Dados do usuário (pontos vêm do LoyaltyUser, não do WalletPass)
        user = getattr(wallet_pass, "user", None)
        if user:
            card_data["user_id"] = user.id
            card_data["customer_name"] = user.get_full_name() if hasattr(user, 'get_full_name') and user.get_full_name() else user.username
            
            # Buscar pontos atuais do LoyaltyUser
            if program:
                try:
                    from zuppy.loyalty.models import LoyaltyUser
                    loyalty_user = LoyaltyUser.objects.filter(program=program, user=user).first()
                    if loyalty_user:
                        card_data["points_balance"] = loyalty_user.points_balance  # ✅ PONTOS CORRETOS
                        logger.info(f"Pontos do usuário: {loyalty_user.points_balance} {program.points_name}")
                    else:
                        card_data["points_balance"] = 0
                        logger.warning(f"LoyaltyUser não encontrado para {user.username} no programa {program.name}")
                except Exception as e:
                    logger.error(f"Erro ao buscar pontos do usuário: {e}")
                    card_data["points_balance"] = 0
            
            # Última mensagem personalizada
            card_data["last_message"] = self._get_last_user_message(user, program)
        
        logger.info(f"Card data gerado: pontos={card_data.get('points_balance')} {card_data.get('points_name')}, cores={card_data.get('background_color')}")
        return card_data

    def _get_last_user_message(self, user, program):
        """Get last message for user in this program."""
        try:
            # Implementar busca da última mensagem do usuário
            # Por enquanto, retorna uma mensagem padrão
            if program and hasattr(program, 'points_name'):
                points_name = program.points_name
                return f"Use seus {points_name} para resgatar prêmios especiais!"
            return "Bem-vindo ao programa de fidelidade!"
        except Exception as e:
            logger.warning(f"Erro ao buscar última mensagem: {e}")
            return None

        """
        Converte um objeto WalletPass para o formato card_data usado internamente.
        """
        card_data = {
            "serial_number": wallet_pass.serial_number,
            "description": wallet_pass.description,
            "barcode_value": wallet_pass.barcode_value,
            "barcode_alt_text": wallet_pass.barcode_alt_text,
            "pass_type": getattr(wallet_pass, 'pass_type', 'storecard'),
            "customer_name": getattr(wallet_pass, 'customer_name', ''),
            "company_name": getattr(wallet_pass, 'company_name', ''),
            "logo_text": getattr(wallet_pass, 'logo_text', ''),
        }
        
        # Adicionar campos de cor se disponíveis
        if hasattr(wallet_pass, 'background_color') and wallet_pass.background_color:
            card_data["background_color"] = wallet_pass.background_color
        if hasattr(wallet_pass, 'foreground_color') and wallet_pass.foreground_color:
            card_data["foreground_color"] = wallet_pass.foreground_color
        if hasattr(wallet_pass, 'label_color') and wallet_pass.label_color:
            card_data["label_color"] = wallet_pass.label_color
            
        # Adicionar pontos se for loyalty card
        if hasattr(wallet_pass, 'points_balance'):
            card_data["points_balance"] = wallet_pass.points_balance
        if hasattr(wallet_pass, 'points_name'):
            card_data["points_name"] = wallet_pass.points_name
            
        # Adicionar informações de template e programa
        if hasattr(wallet_pass, 'template_id'):
            card_data["template_id"] = wallet_pass.template_id
        if hasattr(wallet_pass, 'loyalty_program_id'):
            card_data["loyalty_program_id"] = wallet_pass.loyalty_program_id
        if hasattr(wallet_pass, 'company_id'):
            card_data["company_id"] = wallet_pass.company_id
            
        return card_data

    def _generate_pass_from_card_data(self, card_data: dict) -> bytes:
        """
        Gera um passe Apple Wallet a partir de dados de cartão.
        
        Esse método é usado pelos serviços de download para gerar passes
        diretamente a partir de dados estruturados.
        
        Args:
            card_data: Dicionário com dados do cartão
            
        Returns:
            bytes: Arquivo .pkpass gerado
        """
        try:
            # Criar estrutura de campos usando nosso método corrigido
            fields_structure = self._create_storecard_fields(card_data)
            
            # Criar objeto pass.json básico
            pass_json = {
                "passTypeIdentifier": card_data.get("pass_type_identifier"),
                "organizationName": card_data.get("organization_name"),
                "serialNumber": card_data.get("serial_number"),
                "description": card_data.get("description"),
                "formatVersion": 1,
                "logoText": card_data.get("logo_text", ""),
                "foregroundColor": card_data.get("foreground_color", "#000000"),
                "backgroundColor": card_data.get("background_color", "#FFFFFF"),
                "labelColor": card_data.get("label_color", "#666666"),
                "storeCard": fields_structure,
                "barcodes": [{
                    "message": card_data.get("qr_value", ""),
                    "format": "PKBarcodeFormatQR",
                    "messageEncoding": "iso-8859-1",
                    "altText": card_data.get("qr_value", "")
                }],
                "barcode": {
                    "message": card_data.get("qr_value", ""),
                    "format": "PKBarcodeFormatQR",
                    "messageEncoding": "iso-8859-1",
                    "altText": card_data.get("qr_value", "")
                }
            }
            
            # Gerar o passe final
            return self._generate_final_pass(pass_json, card_data)
            
        except Exception as e:
            logger.error(f"Erro ao gerar passe a partir de card_data: {e}")
            raise