import tempfile
import pytest
# AVISO: Este teste foi descontinuado pois pass_generator.py foi removido na refatoração.
# O teste abaixo é mantido apenas para referência histórica.
# Para testar a geração de manifestos, utilize apple_pass_generator.py diretamente.

# from zuppy.wallet.infrastructure.apple.apple_pass_generator import ApplePassGenerator

# class DummyPassGenerator(ApplePassGenerator):
#     """
#     Subclasse para permitir instanciar ApplePassGenerator sem dependências externas reais.
#     """
#     def __init__(self):
#         # Inicialize apenas o necessário para o teste
#         pass
#
# def test_generate_manifest_ignores_signature_and_manifest(tmp_path):
#     # Arrange: cria arquivos de teste
#     files_content = {
#         "pass.json": b'{"foo": "bar"}',
#         "icon.png": b"fakeimage",
#         "manifest.json": b"should be ignored",
#         "signature": b"should be ignored"
#     }
#     for filename, content in files_content.items():
#         (tmp_path / filename).write_bytes(content)
#
#     generator = DummyPassGenerator()
#     # Act
#     manifest_path = generator._generate_manifest(str(tmp_path))
#     assert os.path.exists(manifest_path)
#     with open(manifest_path, "r", encoding="utf-8") as f:
#         manifest = json.load(f)
#
#     # Assert: apenas arquivos relevantes presentes
#     expected_files = ["pass.json", "icon.png"]
#     for fname in expected_files:
#         file_path = tmp_path / fname
#         with open(file_path, "rb") as f:
#             expected_hash = hashlib.sha1(f.read()).hexdigest()
#         assert manifest[fname] == expected_hash
#
#     # Arquivos ignorados não devem aparecer
#     assert "manifest.json" not in manifest
#     assert "signature" not in manifest
#
#     # O arquivo manifest.json deve estar bem formatado
#     assert isinstance(manifest, dict)
#     assert all(isinstance(v, str) for v in manifest.values())


# AVISO: Este teste foi descontinuado pois pass_generator.py foi removido na refatoração.
# O teste abaixo é mantido apenas para referência histórica.
# Para testar a geração de manifestos, utilize apple_pass_generator.py diretamente.

# class DummyPassGenerator(ApplePassGenerator):
#     """
#     Subclasse para permitir instanciar ApplePassGenerator sem dependências externas reais.
#     """
#     def __init__(self):
#         # Inicialize apenas o necessário para o teste
#         pass
#
# def test_generate_manifest_ignores_signature_and_manifest(tmp_path):
#     # Arrange: cria arquivos de teste
#     files_content = {
#         "pass.json": b'{"foo": "bar"}',
#         "icon.png": b"fakeimage",
#         "manifest.json": b"should be ignored",
#         "signature": b"should be ignored"
#     }
#     for filename, content in files_content.items():
#         (tmp_path / filename).write_bytes(content)
#
#     generator = DummyPassGenerator()
#     # Act
#     manifest_path = generator._generate_manifest(str(tmp_path))
#     assert os.path.exists(manifest_path)
#     with open(manifest_path, "r", encoding="utf-8") as f:
#         manifest = json.load(f)
#
#     # Assert: apenas arquivos relevantes presentes
#     expected_files = ["pass.json", "icon.png"]
#     for fname in expected_files:
#         file_path = tmp_path / fname
#         with open(file_path, "rb") as f:
#             expected_hash = hashlib.sha1(f.read()).hexdigest()
#         assert manifest[fname] == expected_hash
#
#     # Arquivos ignorados não devem aparecer
#     assert "manifest.json" not in manifest
#     assert "signature" not in manifest
#
#     # O arquivo manifest.json deve estar bem formatado
#     assert isinstance(manifest, dict)
#     assert all(isinstance(v, str) for v in manifest.values())
